# =======================================================================
# Browser RPA (MidScene) Environment Configuration
# =======================================================================

# =======================================================================
# Server Configuration
# =======================================================================
PORT=3000
NODE_ENV=development

# =======================================================================
# AI Model Configuration (Simplified)
# =======================================================================
# LLM Configuration (Large Language Model for text-based planning)
LLM_API_KEY=your_llm_api_key_here
LLM_ENDPOINT=https://openrouter.ai/api/v1/chat/completions
LLM_MODEL=openrouter/optimus-alpha
LLM_MAX_TOKENS=8192
LLM_TEMPERATURE=0.2
LLM_TIMEOUT=30000

# VLM Configuration (Vision Language Model for screenshot analysis)
VLM_API_KEY=your_vlm_api_key_here
VLM_ENDPOINT=https://openrouter.ai/api/v1/chat/completions
VLM_MODEL=gpt-4-vision-preview
VLM_MAX_TOKENS=4096
VLM_TEMPERATURE=0.2
VLM_TIMEOUT=30000

# Alternative: Use same provider for both LLM and VLM
# OPENAI_API_KEY=your_openai_key_here
# OPENAI_API_URL=https://openrouter.ai/api/v1/chat/completions
# OPENAI_MODEL=openrouter/optimus-alpha

# =======================================================================
# Proxy Configuration (optional)
# =======================================================================
# HTTP proxy for API requests
HTTP_PROXY=

# HTTPS proxy for API requests
HTTPS_PROXY=

# SOCKS proxy for API requests
SOCKS_PROXY=

# =======================================================================
# File Storage Configuration
# =======================================================================
# Upload directory for screenshots and DOM trees
UPLOAD_DIR=uploads

# Maximum file sizes (in bytes)
MAX_SCREENSHOT_SIZE=5242880  # 5MB
MAX_DOM_TREE_SIZE=10485760   # 10MB
MAX_INSTRUCTION_LENGTH=1000

# =======================================================================
# Rate Limiting Configuration
# =======================================================================
# API rate limiting (requests per minute)
RATE_LIMIT_REQUESTS=10
RATE_LIMIT_WINDOW=60000  # 60 seconds

# =======================================================================
# Retry Configuration
# =======================================================================
# Maximum retry attempts for automation actions
ACTION_MAX_RETRIES=3
ACTION_RETRY_DELAY=1000

# =======================================================================
# Security Configuration
# =======================================================================
# Enable/disable request logging
ENABLE_REQUEST_LOGGING=true

# Enable/disable detailed error responses
ENABLE_DETAILED_ERRORS=true

# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =======================================================================
# Database Configuration
# =======================================================================
# SQLite database path
DB_PATH=./database.db

# Data Retention Configuration
RETENTION_PERIOD_DAYS=30
MAX_RECORDS_PER_TABLE=10000
CLEANUP_SCREENSHOTS=true
CLEANUP_INTERVAL=86400000
ENABLE_SCHEDULED_CLEANUP=false

# =======================================================================
# Monitoring Configuration
# =======================================================================
# Enable health checks
ENABLE_HEALTH_CHECKS=true

# Enable metrics collection
ENABLE_METRICS=true

# =======================================================================
# Development Configuration
# =======================================================================
# Enable debug logging
DEBUG_MODE=false

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable API documentation
ENABLE_API_DOCS=true