# Browser RPA (MidScene) 🤖

An AI-powered Browser Robotic Process Automation system that combines LLM planning with real browser automation.

## 🚀 Features

- **AI-Powered Planning**: Uses LLMs to generate automation plans from natural language instructions
- **Real Browser Automation**: Executes actions using <PERSON>wright for precise web interactions
- **Visual Feedback**: Captures screenshots and provides visual confirmation of actions
- **Robust Error Handling**: Comprehensive retry mechanisms and error recovery
- **Production Ready**: PM2 support, monitoring, and scalable architecture

## 📋 Prerequisites

- **Node.js** 18+ 
- **npm** or **yarn**
- **PM2** (optional, for process management)
- **LLM API Key** (OpenAI, OpenRouter, etc.)

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd bs-BrowserRPA
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

**Required Configuration:**
```env
# LLM Configuration (for text-based planning)
LLM_API_KEY=your_llm_api_key_here
LLM_ENDPOINT=https://openrouter.ai/api/v1/chat/completions
LLM_MODEL=openrouter/optimus-alpha

# VLM Configuration (for screenshot analysis)
VLM_API_KEY=your_vlm_api_key_here  
VLM_ENDPOINT=https://openrouter.ai/api/v1/chat/completions
VLM_MODEL=gpt-4-vision-preview

# Or use legacy OpenAI configuration
# OPENAI_API_KEY=your_api_key_here
# OPENAI_API_URL=https://openrouter.ai/api/v1/chat/completions
# OPENAI_MODEL=openrouter/optimus-alpha
```

### 3. Install Dependencies
```bash
# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 4. Build and Run
```bash
# Build both components
cd server && npm run build
cd ../client && npm run build

# Start the server
cd server && npm start

# In another terminal, run the client
cd client && npm start
```

## 🔧 Production Deployment

### PM2 Process Manager
```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
pm2 start ecosystem.config.js

# View status and logs
pm2 status
pm2 logs

# Monitor processes
pm2 monit

# Stop services
pm2 stop all
```

### Manual Production Setup
```bash
# Build and start
cd server && npm run build && npm start &
cd ../client && npm run build && npm start &

# With environment variables
NODE_ENV=production PORT=3000 npm start
```

## 📁 Project Structure

```
bs-BrowserRPA/
├── client/                 # Browser automation client
│   ├── src/
│   │   ├── api.ts         # API client with retry logic
│   │   ├── executor.ts    # Real browser automation
│   │   ├── executor-enhanced.ts  # Enhanced executor with error handling
│   │   └── retry-handler.ts     # Retry mechanisms
│   └── package.json
├── server/                 # API server
│   ├── src/
│   │   ├── app.ts         # Express server
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic
│   │   └── types/         # TypeScript types
│   └── package.json
├── nginx/                  # Reverse proxy configuration
├── ecosystem.config.js    # PM2 process configuration
├── .env.example          # Environment template
└── README.md
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LLM_API_KEY` | LLM API key | *Required* |
| `LLM_ENDPOINT` | LLM API endpoint | OpenRouter |
| `LLM_MODEL` | LLM model name | optimus-alpha |
| `VLM_API_KEY` | VLM API key | *Required* |
| `VLM_ENDPOINT` | VLM API endpoint | OpenRouter |
| `VLM_MODEL` | VLM model name | gpt-4-vision |
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `RATE_LIMIT_REQUESTS` | Rate limit | 10/min |
| `MAX_SCREENSHOT_SIZE` | Max image size | 5MB |
| `MAX_DOM_TREE_SIZE` | Max DOM size | 10MB |

### Retry Configuration

```env
# Action retry settings
ACTION_MAX_RETRIES=3
ACTION_RETRY_DELAY=1000
```

## 📊 API Endpoints

### Plan Generation
```bash
POST /api/plan/generate
{
  "userInstruction": "Click the login button",
  "conversationHistory": [],
  "size": {"width": 1920, "height": 1080},
  "domId": "optional-dom-id"
}
```

### DOM Upload
```bash
POST /api/plan/upload/dom
{
  "domTree": {
    "rootId": "0",
    "map": {...}
  }
}
```

### Result Upload
```bash
POST /api/plan/result
{
  "planId": "plan-uuid",
  "results": [...],
  "overallStatus": "success|failed"
}
```

### Health Check
```bash
GET /api/plan/health
```

## 🧪 Testing

### Unit Tests
```bash
# Run client tests
cd client && npm test

# Run server tests
cd server && npm test
```

### End-to-End Tests
```bash
# Run Playwright tests
cd client && npm run test:e2e
```

### Load Testing
```bash
# Using artillery.io
npm install -g artillery
artillery run load-test.yml
```

## 🔍 Monitoring

### Health Checks
- Server health: `GET /api/plan/health`
- Execution stats: `GET /api/plan/stats`
- System metrics: Built-in monitoring

### Logging
```bash
# View PM2 logs
pm2 logs

# View specific process logs
pm2 logs browser-rpa-server

# View nginx logs (if using nginx)
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Metrics
- Request rate and response times
- Success/failure rates
- Resource usage
- Error tracking

## 🛡️ Security

### Rate Limiting
- API calls: 10 requests/minute
- Uploads: 5 requests/minute
- Configurable via environment variables

### Input Validation
- File size limits
- Content type validation
- Request structure validation
- XSS protection

### Network Security
- HTTPS support
- CORS configuration
- Security headers
- Proxy protection

## 🚨 Troubleshooting

### Common Issues

**1. LLM API Errors**
```bash
# Check API key and URL
curl -H "Authorization: Bearer $LLM_API_KEY" $LLM_ENDPOINT

# Enable debug logging
DEBUG_MODE=true npm start
```

**2. Browser Automation Failures**
```bash
# Install browser dependencies
npx playwright install-deps

# Run in headed mode for debugging
HEADLESS=false npm start
```

**3. PM2 Process Issues**
```bash
# Restart processes
pm2 restart all

# Reset PM2
pm2 kill
pm2 start ecosystem.config.js

# View process details
pm2 show browser-rpa-server
```

**4. Network Connectivity**
```bash
# Check service health
curl http://localhost:3000/api/plan/health

# Test with proxy
HTTP_PROXY=http://proxy:8080 npm start
```

### Debug Mode
```bash
# Enable verbose logging
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_DETAILED_ERRORS=true
```

## 📈 Performance Optimization

### Scaling
- Horizontal scaling with PM2 cluster mode
- Load balancing with Nginx
- SQLite database for persistence
- File cleanup for uploads

### Resource Management
- Memory monitoring with PM2
- CPU allocation via PM2 cluster
- Disk cleanup for uploads
- Process monitoring and restart

## 🔄 Development Workflow

### Local Development
```bash
# Watch mode for development
cd server && npm run dev
cd client && npm run dev
```

### Code Quality
```bash
# Linting
npm run lint

# Type checking
npm run type-check

# Formatting
npm run format
```

### Git Workflow
```bash
# Feature branch
git checkout -b feature/new-feature

# Commit changes
git commit -m "Add new feature"

# Push and create PR
git push origin feature/new-feature
```

## 📚 Architecture

### System Components
1. **Client**: Browser automation executor
2. **Server**: API service and LLM integration
3. **Database**: SQLite for persistence
4. **PM2**: Process management and monitoring

### Data Flow
1. User instruction → Server
2. Server → LLM for plan generation
3. Plan → Client for execution
4. Execution results → Server
5. Results stored and analyzed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: Create a GitHub issue
- **Documentation**: Check the `/docs` folder
- **Community**: Join our Discord server

## 🚀 Roadmap

- [x] Database integration (SQLite)
- [x] Analytics and reporting
- [x] Data retention policies
- [ ] Advanced scheduling
- [ ] Multi-browser support
- [ ] Plugin system
- [ ] Web UI dashboard
- [ ] Cloud deployment templates