"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.executePlan = executePlan;
const retry_handler_1 = require("./retry-handler");
/**
 * Enhanced browser automation executor with retry and error handling
 */
async function executePlan(plan, context = {}) {
    const results = [];
    let overallStatus = 'success';
    let errorMessage = '';
    const { page, timeout = 10000, retryConfig, enableRetry = true } = context;
    // Initialize retry handler
    const retryHandler = new retry_handler_1.RetryHandler({
        ...retry_handler_1.DEFAULT_RETRY_CONFIG,
        ...retryConfig,
    });
    // If no page context provided, fall back to document-based execution
    const isPlaywrightMode = !!page;
    console.log(`🚀 开始执行计划: ${plan.planId}, 共${plan.actions.length}个步骤`);
    for (let i = 0; i < plan.actions.length; i++) {
        const action = plan.actions[i];
        let stepResult;
        try {
            console.log(`📍 执行第${i + 1}步: ${action.type} - ${action.thought || ''}`);
            if (enableRetry) {
                // Execute with retry logic
                stepResult = await retryHandler.executeWithRetry(() => executeAction(action, isPlaywrightMode ? page : null, timeout), `Step ${i + 1} (${action.type})`, {
                    // Custom retry config for different action types
                    maxAttempts: getMaxAttemptsForAction(action.type),
                    retryableErrors: getRetryableErrorsForAction(action.type),
                });
            }
            else {
                // Execute without retry
                stepResult = await executeAction(action, isPlaywrightMode ? page : null, timeout);
            }
            stepResult.step = i + 1;
            stepResult.id = action.id;
            results.push(stepResult);
            if (stepResult.status === 'failed') {
                overallStatus = 'failed';
                errorMessage = stepResult.log || '执行失败';
                console.log(`❌ 步骤${i + 1}失败，终止执行`);
                break;
            }
            console.log(`✅ 步骤${i + 1}完成`);
            // Add small delay between steps to avoid overwhelming the system
            if (i < plan.actions.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }
        catch (err) {
            overallStatus = 'failed';
            errorMessage = err.message || '执行失败';
            console.log(`💥 步骤${i + 1}异常: ${errorMessage}`);
            results.push({
                step: i + 1,
                id: action.id,
                status: 'failed',
                screenshot: await captureScreenshot(page),
                log: errorMessage,
            });
            break;
        }
    }
    console.log(`🏁 计划执行完成: ${overallStatus}`);
    return { results, overallStatus, errorMessage };
}
/**
 * Execute individual action with timeout and enhanced error handling
 */
async function executeAction(action, page, timeout) {
    const screenshot = await captureScreenshot(page);
    return retry_handler_1.TimeoutHandler.withTimeout(async () => {
        try {
            switch (action.type) {
                case 'Locate':
                    return await executeLocate(action, page, timeout, screenshot);
                case 'Tap':
                    return await executeTap(action, page, timeout, screenshot);
                case 'Input':
                    return await executeInput(action, page, timeout, screenshot);
                case 'Scroll':
                    return await executeScroll(action, page, timeout, screenshot);
                case 'KeyboardPress':
                    return await executeKeyboardPress(action, page, timeout, screenshot);
                case 'Drag':
                    return await executeDrag(action, page, timeout, screenshot);
                case 'Finished':
                    return {
                        step: 0,
                        status: 'success',
                        screenshot,
                        log: '执行完成',
                    };
                default:
                    throw new Error(`未知操作类型: ${action.type}`);
            }
        }
        catch (error) {
            return {
                step: 0,
                status: 'failed',
                screenshot: await captureScreenshot(page),
                log: error.message,
            };
        }
    }, timeout, `Action ${action.type}`);
}
/**
 * Get max retry attempts for different action types
 */
function getMaxAttemptsForAction(actionType) {
    switch (actionType) {
        case 'Locate':
            return 5; // Elements might need time to appear
        case 'Tap':
            return 3; // Clicks might fail due to timing
        case 'Input':
            return 2; // Input usually works or doesn't
        case 'Scroll':
            return 2; // Scroll operations are generally reliable
        case 'KeyboardPress':
            return 2;
        case 'Drag':
            return 3; // Drag operations can be timing-sensitive
        default:
            return 2;
    }
}
/**
 * Get retryable errors for different action types
 */
function getRetryableErrorsForAction(actionType) {
    const commonErrors = ['timeout', 'network', 'element not found', 'element not visible'];
    switch (actionType) {
        case 'Locate':
            return [...commonErrors, 'element not ready', 'stale element'];
        case 'Tap':
            return [...commonErrors, 'element not clickable', 'element obscured'];
        case 'Input':
            return [...commonErrors, 'element not focusable', 'element read-only'];
        case 'Scroll':
            return [...commonErrors, 'scroll failed'];
        case 'KeyboardPress':
            return [...commonErrors, 'key event failed'];
        case 'Drag':
            return [...commonErrors, 'drag failed', 'element not draggable'];
        default:
            return commonErrors;
    }
}
/**
 * Locate element using bbox or XPath with enhanced error handling
 */
async function executeLocate(action, page, timeout, screenshot) {
    const { locate } = action;
    if (!locate) {
        throw new Error('缺少定位信息');
    }
    try {
        if (page) {
            // Playwright mode
            if (locate.bbox) {
                const { x, y, width, height } = locate.bbox;
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                // Wait for page to be ready
                await page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => { });
                // Check if element exists at coordinates
                const element = await page.locator('body').elementHandle();
                if (element) {
                    const box = await element.boundingBox();
                    if (box && centerX >= box.x && centerX <= box.x + box.width &&
                        centerY >= box.y && centerY <= box.y + box.height) {
                        return {
                            step: 0,
                            status: 'success',
                            screenshot,
                            log: `成功定位元素: ${locate.prompt}`,
                        };
                    }
                }
                throw new Error(`Element not found at coordinates (${centerX}, ${centerY})`);
            }
        }
        else {
            // Document mode - use DOM queries
            let element = null;
            // Try bbox first, then XPath as fallback
            try {
                element = findElementByBbox(locate.bbox);
            }
            catch (bboxError) {
                if (locate.xpath) {
                    element = findElementByXPath(locate.xpath);
                }
                else {
                    throw bboxError;
                }
            }
            // Verify element is visible
            if (element && element instanceof HTMLElement) {
                const rect = element.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {
                    throw new Error('Element not visible');
                }
                return {
                    step: 0,
                    status: 'success',
                    screenshot,
                    log: `成功定位元素: ${locate.prompt}`,
                };
            }
        }
        throw new Error(`无法定位元素: ${locate.prompt}`);
    }
    catch (err) {
        throw new Error(`定位失败: ${err.message}`);
    }
}
/**
 * Click element with enhanced error handling
 */
async function executeTap(action, page, timeout, screenshot) {
    const { locate } = action;
    if (!locate) {
        throw new Error('缺少点击目标定位信息');
    }
    try {
        if (page) {
            // Playwright mode
            if (locate.bbox) {
                const { x, y, width, height } = locate.bbox;
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                // Wait for page to be ready
                await page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => { });
                // Click at coordinates
                await page.mouse.click(centerX, centerY);
                // Wait a bit for the click to register
                await page.waitForTimeout(100);
            }
            else {
                throw new Error('缺少坐标信息');
            }
        }
        else {
            // Document mode
            let element = null;
            // Try bbox first, then XPath as fallback
            try {
                element = findElementByBbox(locate.bbox);
            }
            catch (bboxError) {
                if (locate.xpath) {
                    element = findElementByXPath(locate.xpath);
                }
                else {
                    throw bboxError;
                }
            }
            if (element && element instanceof HTMLElement) {
                // Check if element is clickable
                const rect = element.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {
                    throw new Error('Element not clickable (hidden)');
                }
                // Scroll element into view if needed
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                // Wait a bit for scroll to complete
                await new Promise(resolve => setTimeout(resolve, 200));
                element.click();
            }
            else {
                throw new Error('无法找到点击目标');
            }
        }
        return {
            step: 0,
            status: 'success',
            screenshot: await captureScreenshot(page),
            log: `成功点击: ${locate.prompt}`,
        };
    }
    catch (err) {
        throw new Error(`点击失败: ${err.message}`);
    }
}
/**
 * Input text with enhanced error handling
 */
async function executeInput(action, page, timeout, screenshot) {
    const { locate, param } = action;
    if (!locate || !param) {
        throw new Error('缺少输入目标或内容');
    }
    try {
        if (page) {
            // Playwright mode
            if (locate.bbox) {
                const { x, y, width, height } = locate.bbox;
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                // Click at coordinates first
                await page.mouse.click(centerX, centerY);
                // Wait for focus
                await page.waitForTimeout(200);
                // Clear existing content and type new content
                await page.keyboard.press('Control+a');
                await page.keyboard.type(param);
            }
        }
        else {
            // Document mode
            let element = null;
            // Try bbox first, then XPath as fallback
            try {
                element = findElementByBbox(locate.bbox);
            }
            catch (bboxError) {
                if (locate.xpath) {
                    element = findElementByXPath(locate.xpath);
                }
                else {
                    throw bboxError;
                }
            }
            if (element && (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement)) {
                // Check if element is editable
                if (element.disabled || element.readOnly) {
                    throw new Error('Input element is disabled or read-only');
                }
                // Focus and set value
                element.focus();
                element.value = param;
                // Dispatch input and change events
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
            else {
                throw new Error('无法找到输入框');
            }
        }
        return {
            step: 0,
            status: 'success',
            screenshot: await captureScreenshot(page),
            log: `成功输入: ${param}`,
        };
    }
    catch (err) {
        throw new Error(`输入失败: ${err.message}`);
    }
}
/**
 * Scroll page with enhanced error handling
 */
async function executeScroll(action, page, timeout, screenshot) {
    try {
        const { param } = action;
        const scrollAmount = param?.amount || 500;
        const direction = param?.direction || 'down';
        if (page) {
            // Playwright mode
            if (direction === 'down') {
                await page.mouse.wheel(0, scrollAmount);
            }
            else if (direction === 'up') {
                await page.mouse.wheel(0, -scrollAmount);
            }
        }
        else {
            // Document mode
            if (direction === 'down') {
                window.scrollBy(0, scrollAmount);
            }
            else if (direction === 'up') {
                window.scrollBy(0, -scrollAmount);
            }
        }
        return {
            step: 0,
            status: 'success',
            screenshot: await captureScreenshot(page),
            log: `成功滚动: ${direction} ${scrollAmount}px`,
        };
    }
    catch (err) {
        throw new Error(`滚动失败: ${err.message}`);
    }
}
/**
 * Press keyboard keys with enhanced error handling
 */
async function executeKeyboardPress(action, page, timeout, screenshot) {
    try {
        const { param } = action;
        const key = param?.key || 'Enter';
        if (page) {
            // Playwright mode
            await page.keyboard.press(key);
        }
        else {
            // Document mode
            const keyboardEvent = new KeyboardEvent('keydown', {
                key: key,
                bubbles: true
            });
            document.dispatchEvent(keyboardEvent);
        }
        return {
            step: 0,
            status: 'success',
            screenshot: await captureScreenshot(page),
            log: `成功按键: ${key}`,
        };
    }
    catch (err) {
        throw new Error(`按键失败: ${err.message}`);
    }
}
/**
 * Drag element with enhanced error handling
 */
async function executeDrag(action, page, timeout, screenshot) {
    try {
        const { locate, param } = action;
        if (!locate || !param) {
            throw new Error('缺少拖拽信息');
        }
        if (page) {
            // Playwright mode
            const { x, y, width, height } = locate.bbox;
            const startX = x + width / 2;
            const startY = y + height / 2;
            const endX = param.x || startX + 100;
            const endY = param.y || startY + 100;
            await page.mouse.move(startX, startY);
            await page.mouse.down();
            await page.mouse.move(endX, endY);
            await page.mouse.up();
        }
        else {
            throw new Error('拖拽操作需要 Playwright 环境');
        }
        return {
            step: 0,
            status: 'success',
            screenshot: await captureScreenshot(page),
            log: '成功执行拖拽',
        };
    }
    catch (err) {
        throw new Error(`拖拽失败: ${err.message}`);
    }
}
/**
 * Capture screenshot with error handling
 */
async function captureScreenshot(page) {
    if (page) {
        try {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            return screenshot;
        }
        catch (err) {
            console.error('Screenshot capture failed:', err);
            return '';
        }
    }
    return ''; // No screenshot in document mode
}
/**
 * Find element by bounding box coordinates with enhanced error handling
 */
function findElementByBbox(bbox) {
    if (!bbox) {
        throw new Error('bbox coordinates not provided');
    }
    const { x, y, width, height } = bbox;
    if (width <= 0 || height <= 0) {
        throw new Error('Invalid bbox dimensions');
    }
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    // Check if coordinates are within viewport
    if (centerX < 0 || centerY < 0 || centerX > window.innerWidth || centerY > window.innerHeight) {
        throw new Error('Element coordinates outside viewport');
    }
    const element = document.elementFromPoint(centerX, centerY);
    if (!element) {
        throw new Error('Element not found at coordinates');
    }
    return element;
}
/**
 * Find element by XPath with enhanced error handling
 */
function findElementByXPath(xpath) {
    if (!xpath) {
        throw new Error('XPath not provided');
    }
    try {
        const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        const element = result.singleNodeValue;
        if (!element) {
            throw new Error(`Element not found with XPath: ${xpath}`);
        }
        return element;
    }
    catch (err) {
        throw new Error(`XPath evaluation failed: ${err}`);
    }
}
