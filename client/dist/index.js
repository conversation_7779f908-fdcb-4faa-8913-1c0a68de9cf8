"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = require("./api");
const executor_1 = require("./executor");
async function main() {
    const userInstruction = '点击登录按钮';
    const conversationHistory = [
        { role: 'user', content: '打开首页' },
        { role: 'assistant', content: '已打开首页' },
    ];
    const size = { width: 1920, height: 1080 };
    // 模拟一个测试页面HTML
    const html = `
    <html>
      <body>
        <div id="container">
          <button id="loginBtn" class="btn">登录</button>
        </div>
      </body>
    </html>
  `;
    // 将测试HTML插入当前页面
    document.body.innerHTML = html;
    // 在当前页面构建DOM树映射
    const { rootId, map } = (0, api_1.buildDomTreeMap)();
    console.log('生成的DOM树映射:', { rootId, map });
    // 上传DOM树
    const domId = await (0, api_1.uploadDomTree)({ rootId, map });
    console.log('上传DOM树成功，domId:', domId);
    console.log('请求生成Plan...');
    const plan = await (0, api_1.requestPlan)(userInstruction, conversationHistory, size, undefined, domId);
    console.log('收到Plan:', JSON.stringify(plan, null, 2));
    console.log('开始执行Plan...');
    const { results, overallStatus, errorMessage } = await (0, executor_1.executePlan)(plan);
    console.log('上传执行结果...');
    const resp = await (0, api_1.uploadResult)(plan.planId, results, overallStatus, errorMessage);
    console.log('上传结果响应:', resp);
}
main().catch((err) => {
    console.error('客户端运行出错:', err);
});
