"use strict";
/**
 * Retry handler for browser automation operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreaker = exports.TimeoutHandler = exports.RetryHandler = exports.DEFAULT_RETRY_CONFIG = void 0;
exports.DEFAULT_RETRY_CONFIG = {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrors: [
        'timeout',
        'network',
        'element not found',
        'element not visible',
        'element not clickable',
        'stale element',
        'connection',
    ],
};
class RetryHandler {
    constructor(config = exports.DEFAULT_RETRY_CONFIG) {
        this.config = config;
    }
    /**
     * Execute operation with retry logic
     */
    async executeWithRetry(operation, operationName, customConfig) {
        const config = { ...this.config, ...customConfig };
        let lastError = null;
        let delay = config.initialDelay;
        for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
            try {
                const result = await operation();
                if (attempt > 1) {
                    console.log(`✅ ${operationName} 成功 (第${attempt}次尝试)`);
                }
                return result;
            }
            catch (error) {
                lastError = error;
                console.log(`❌ ${operationName} 失败 (第${attempt}次尝试): ${lastError.message}`);
                // Check if error is retryable
                if (!this.isRetryableError(lastError, config.retryableErrors)) {
                    console.log(`🚫 错误不可重试，停止尝试`);
                    throw lastError;
                }
                // Don't retry on final attempt
                if (attempt === config.maxAttempts) {
                    console.log(`🔴 达到最大重试次数 (${config.maxAttempts})`);
                    throw lastError;
                }
                // Wait before retry with exponential backoff
                console.log(`⏳ 等待 ${delay}ms 后重试...`);
                await this.sleep(delay);
                delay = Math.min(delay * config.backoffFactor, config.maxDelay);
            }
        }
        throw lastError || new Error(`${operationName} 失败`);
    }
    /**
     * Check if error is retryable
     */
    isRetryableError(error, retryableErrors) {
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.some(retryableError => errorMessage.includes(retryableError.toLowerCase()));
    }
    /**
     * Sleep utility
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.RetryHandler = RetryHandler;
/**
 * Timeout wrapper for operations
 */
class TimeoutHandler {
    static async withTimeout(operation, timeoutMs, operationName = 'Operation') {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`${operationName} timeout after ${timeoutMs}ms`));
            }, timeoutMs);
        });
        try {
            return await Promise.race([operation(), timeoutPromise]);
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('timeout')) {
                console.log(`⏰ ${operationName} 超时 (${timeoutMs}ms)`);
            }
            throw error;
        }
    }
}
exports.TimeoutHandler = TimeoutHandler;
/**
 * Circuit breaker pattern for preventing cascading failures
 */
class CircuitBreaker {
    constructor(failureThreshold = 5, recoveryTimeout = 60000 // 1 minute
    ) {
        this.failureThreshold = failureThreshold;
        this.recoveryTimeout = recoveryTimeout;
        this.failureCount = 0;
        this.lastFailureTime = 0;
        this.state = 'CLOSED';
    }
    async execute(operation, operationName) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
                this.state = 'HALF_OPEN';
                console.log(`🔄 Circuit breaker for ${operationName} is HALF_OPEN`);
            }
            else {
                throw new Error(`Circuit breaker is OPEN for ${operationName}`);
            }
        }
        try {
            const result = await operation();
            this.onSuccess(operationName);
            return result;
        }
        catch (error) {
            this.onFailure(operationName);
            throw error;
        }
    }
    onSuccess(operationName) {
        this.failureCount = 0;
        if (this.state === 'HALF_OPEN') {
            this.state = 'CLOSED';
            console.log(`✅ Circuit breaker for ${operationName} is CLOSED`);
        }
    }
    onFailure(operationName) {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        if (this.failureCount >= this.failureThreshold) {
            this.state = 'OPEN';
            console.log(`🔴 Circuit breaker for ${operationName} is OPEN`);
        }
    }
    getState() {
        return this.state;
    }
}
exports.CircuitBreaker = CircuitBreaker;
