"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
test_1.test.describe('Analytics API Integration Tests', () => {
    const baseURL = 'http://localhost:3000/api/analytics';
    test_1.test.beforeEach(async () => {
        await (0, test_helpers_1.verifyServerHealth)();
    });
    (0, test_1.test)('should get dashboard statistics', async ({ request }) => {
        const response = await request.get(`${baseURL}/dashboard`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const dashboard = await response.json();
        (0, test_1.expect)(dashboard).toBeTruthy();
        // Verify dashboard structure
        if (dashboard.totalPlans !== undefined) {
            (0, test_1.expect)(typeof dashboard.totalPlans).toBe('number');
        }
        if (dashboard.totalExecutions !== undefined) {
            (0, test_1.expect)(typeof dashboard.totalExecutions).toBe('number');
        }
        if (dashboard.successRate !== undefined) {
            (0, test_1.expect)(typeof dashboard.successRate).toBe('number');
            (0, test_1.expect)(dashboard.successRate).toBeGreaterThanOrEqual(0);
            (0, test_1.expect)(dashboard.successRate).toBeLessThanOrEqual(100);
        }
    });
    (0, test_1.test)('should get execution statistics with different time ranges', async ({ request }) => {
        const timeRanges = ['1d', '7d', '30d'];
        for (const timeRange of timeRanges) {
            const response = await request.get(`${baseURL}/executions/stats?timeRange=${timeRange}&groupBy=day`);
            (0, test_1.expect)(response.ok()).toBeTruthy();
            const stats = await response.json();
            (0, test_1.expect)(stats).toBeTruthy();
            (0, test_1.expect)(Array.isArray(stats) || typeof stats === 'object').toBeTruthy();
        }
    });
    (0, test_1.test)('should get plan performance metrics', async ({ request }) => {
        const response = await request.get(`${baseURL}/plans/performance?limit=10`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const performance = await response.json();
        (0, test_1.expect)(performance).toBeTruthy();
        if (Array.isArray(performance)) {
            (0, test_1.expect)(performance.length).toBeLessThanOrEqual(10);
            performance.forEach((plan) => {
                if (plan.planId) {
                    (0, test_1.expect)(typeof plan.planId).toBe('string');
                }
                if (plan.executionTime !== undefined) {
                    (0, test_1.expect)(typeof plan.executionTime).toBe('number');
                    (0, test_1.expect)(plan.executionTime).toBeGreaterThanOrEqual(0);
                }
            });
        }
    });
    (0, test_1.test)('should get action frequency analysis', async ({ request }) => {
        const response = await request.get(`${baseURL}/actions/frequency?timeRange=7d`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const frequency = await response.json();
        (0, test_1.expect)(frequency).toBeTruthy();
        if (Array.isArray(frequency)) {
            frequency.forEach((action) => {
                if (action.actionType) {
                    (0, test_1.expect)(typeof action.actionType).toBe('string');
                }
                if (action.count !== undefined) {
                    (0, test_1.expect)(typeof action.count).toBe('number');
                    (0, test_1.expect)(action.count).toBeGreaterThanOrEqual(0);
                }
            });
        }
    });
    (0, test_1.test)('should get error analysis', async ({ request }) => {
        const response = await request.get(`${baseURL}/errors/analysis?timeRange=7d&limit=20`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const errors = await response.json();
        (0, test_1.expect)(errors).toBeTruthy();
        if (Array.isArray(errors)) {
            (0, test_1.expect)(errors.length).toBeLessThanOrEqual(20);
            errors.forEach((error) => {
                if (error.errorType) {
                    (0, test_1.expect)(typeof error.errorType).toBe('string');
                }
                if (error.count !== undefined) {
                    (0, test_1.expect)(typeof error.count).toBe('number');
                    (0, test_1.expect)(error.count).toBeGreaterThanOrEqual(0);
                }
            });
        }
    });
    (0, test_1.test)('should get success rate trends', async ({ request }) => {
        const response = await request.get(`${baseURL}/trends/success-rate?timeRange=7d&groupBy=day`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const trends = await response.json();
        (0, test_1.expect)(trends).toBeTruthy();
        if (Array.isArray(trends)) {
            trends.forEach((trend) => {
                if (trend.date) {
                    (0, test_1.expect)(typeof trend.date).toBe('string');
                }
                if (trend.successRate !== undefined) {
                    (0, test_1.expect)(typeof trend.successRate).toBe('number');
                    (0, test_1.expect)(trend.successRate).toBeGreaterThanOrEqual(0);
                    (0, test_1.expect)(trend.successRate).toBeLessThanOrEqual(100);
                }
            });
        }
    });
    (0, test_1.test)('should handle invalid time range parameters', async ({ request }) => {
        const response = await request.get(`${baseURL}/executions/stats?timeRange=invalid&groupBy=day`);
        // Should either return default data or appropriate error
        if (response.ok()) {
            const stats = await response.json();
            (0, test_1.expect)(stats).toBeTruthy();
        }
        else {
            (0, test_1.expect)(response.status()).toBeGreaterThanOrEqual(400);
        }
    });
    (0, test_1.test)('should handle invalid limit parameters', async ({ request }) => {
        const response = await request.get(`${baseURL}/plans/performance?limit=-1`);
        // Should either return default data or appropriate error
        if (response.ok()) {
            const performance = await response.json();
            (0, test_1.expect)(performance).toBeTruthy();
        }
        else {
            (0, test_1.expect)(response.status()).toBeGreaterThanOrEqual(400);
        }
    });
    (0, test_1.test)('should handle large limit parameters', async ({ request }) => {
        const response = await request.get(`${baseURL}/plans/performance?limit=10000`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const performance = await response.json();
        (0, test_1.expect)(performance).toBeTruthy();
        // Should handle large limits gracefully (either cap the results or return all available)
        if (Array.isArray(performance)) {
            (0, test_1.expect)(performance.length).toBeGreaterThanOrEqual(0);
        }
    });
    (0, test_1.test)('should return consistent data structure across endpoints', async ({ request }) => {
        const endpoints = [
            '/dashboard',
            '/executions/stats?timeRange=7d',
            '/plans/performance?limit=5',
            '/actions/frequency?timeRange=7d',
            '/errors/analysis?timeRange=7d',
            '/trends/success-rate?timeRange=7d'
        ];
        for (const endpoint of endpoints) {
            const response = await request.get(`${baseURL}${endpoint}`);
            (0, test_1.expect)(response.ok()).toBeTruthy();
            const data = await response.json();
            (0, test_1.expect)(data).toBeTruthy();
            // Verify response is valid JSON
            (0, test_1.expect)(typeof data).toBe('object');
        }
    });
    (0, test_1.test)('should handle concurrent analytics requests', async ({ request }) => {
        const promises = [
            request.get(`${baseURL}/dashboard`),
            request.get(`${baseURL}/executions/stats?timeRange=7d`),
            request.get(`${baseURL}/plans/performance?limit=10`),
            request.get(`${baseURL}/actions/frequency?timeRange=7d`),
            request.get(`${baseURL}/errors/analysis?timeRange=7d`)
        ];
        const responses = await Promise.all(promises);
        // All requests should succeed
        responses.forEach(response => {
            (0, test_1.expect)(response.ok()).toBeTruthy();
        });
        // Parse all responses
        const results = await Promise.all(responses.map(r => r.json()));
        // All results should be valid
        results.forEach(result => {
            (0, test_1.expect)(result).toBeTruthy();
            (0, test_1.expect)(typeof result).toBe('object');
        });
    });
    (0, test_1.test)('should handle different groupBy parameters', async ({ request }) => {
        const groupByOptions = ['hour', 'day', 'week', 'month'];
        for (const groupBy of groupByOptions) {
            const response = await request.get(`${baseURL}/executions/stats?timeRange=7d&groupBy=${groupBy}`);
            // Should either succeed or handle invalid groupBy gracefully
            if (response.ok()) {
                const stats = await response.json();
                (0, test_1.expect)(stats).toBeTruthy();
            }
            else {
                (0, test_1.expect)(response.status()).toBeGreaterThanOrEqual(400);
            }
        }
    });
    (0, test_1.test)('should validate response times for analytics queries', async ({ request }) => {
        const startTime = Date.now();
        const response = await request.get(`${baseURL}/dashboard`);
        const responseTime = Date.now() - startTime;
        (0, test_1.expect)(response.ok()).toBeTruthy();
        // Analytics queries should be reasonably fast (less than 10 seconds)
        (0, test_1.expect)(responseTime).toBeLessThan(10000);
        const dashboard = await response.json();
        (0, test_1.expect)(dashboard).toBeTruthy();
    });
    (0, test_1.test)('should handle empty data scenarios', async ({ request }) => {
        // Test analytics endpoints when there might be no data
        const response = await request.get(`${baseURL}/plans/performance?limit=100`);
        (0, test_1.expect)(response.ok()).toBeTruthy();
        const performance = await response.json();
        (0, test_1.expect)(performance).toBeTruthy();
        // Should handle empty results gracefully
        if (Array.isArray(performance)) {
            (0, test_1.expect)(performance.length).toBeGreaterThanOrEqual(0);
        }
        else {
            (0, test_1.expect)(typeof performance).toBe('object');
        }
    });
});
