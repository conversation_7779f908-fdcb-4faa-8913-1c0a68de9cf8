"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
const api_1 = require("../../api");
test_1.test.describe('DOM Tree Extraction Tests', () => {
    test_1.test.beforeEach(async () => {
        await (0, test_helpers_1.verifyServerHealth)();
    });
    (0, test_1.test)('should extract DOM tree from simple HTML page', async ({ page }) => {
        const testHtml = `
      <div id="container">
        <h1>Test Page</h1>
        <button id="testBtn" class="btn">Click Me</button>
        <input id="testInput" type="text" placeholder="Enter text">
        <p>This is a test paragraph.</p>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        // Verify DOM tree structure
        (0, test_1.expect)(domTree.rootId).toBeTruthy();
        (0, test_1.expect)(Object.keys(domTree.map).length).toBeGreaterThan(0);
        // Find specific elements in the DOM tree
        const elements = Object.values(domTree.map);
        const buttonElement = elements.find((el) => el.tagName === 'button' && el.attributes?.id === 'testBtn');
        const inputElement = elements.find((el) => el.tagName === 'input' && el.attributes?.id === 'testInput');
        (0, test_1.expect)(buttonElement).toBeTruthy();
        (0, test_1.expect)(inputElement).toBeTruthy();
        (0, test_1.expect)(buttonElement.attributes.class).toBe('btn');
        (0, test_1.expect)(inputElement.attributes.type).toBe('text');
    });
    (0, test_1.test)('should extract DOM tree from complex nested structure', async ({ page }) => {
        const testHtml = `
      <div id="app">
        <header>
          <nav>
            <ul>
              <li><a href="#home">Home</a></li>
              <li><a href="#about">About</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </nav>
        </header>
        <main>
          <section id="content">
            <article>
              <h2>Article Title</h2>
              <p>Article content goes here.</p>
              <div class="actions">
                <button class="btn primary">Save</button>
                <button class="btn secondary">Cancel</button>
              </div>
            </article>
          </section>
          <aside>
            <div class="widget">
              <h3>Widget Title</h3>
              <form>
                <input type="email" placeholder="Email">
                <textarea placeholder="Message"></textarea>
                <button type="submit">Submit</button>
              </form>
            </div>
          </aside>
        </main>
        <footer>
          <p>&copy; 2024 Test Company</p>
        </footer>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        // Verify complex structure
        (0, test_1.expect)(domTree.rootId).toBeTruthy();
        (0, test_1.expect)(Object.keys(domTree.map).length).toBeGreaterThan(10);
        // Verify hierarchical relationships
        const elements = Object.values(domTree.map);
        const navElement = elements.find((el) => el.tagName === 'nav');
        const formElement = elements.find((el) => el.tagName === 'form');
        const buttons = elements.filter((el) => el.tagName === 'button');
        (0, test_1.expect)(navElement).toBeTruthy();
        (0, test_1.expect)(formElement).toBeTruthy();
        (0, test_1.expect)(buttons.length).toBe(3); // Save, Cancel, Submit
    });
    (0, test_1.test)('should handle DOM tree with dynamic content', async ({ page }) => {
        const testHtml = `
      <div id="dynamic-container">
        <button id="addBtn">Add Item</button>
        <ul id="itemList"></ul>
      </div>
      <script>
        document.getElementById('addBtn').addEventListener('click', function() {
          const list = document.getElementById('itemList');
          const item = document.createElement('li');
          item.textContent = 'Item ' + (list.children.length + 1);
          item.className = 'list-item';
          list.appendChild(item);
        });
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        // Extract initial DOM tree
        let domTree = await (0, test_helpers_1.extractDomTree)(page);
        const initialElementCount = Object.keys(domTree.map).length;
        // Add dynamic content
        await page.click('#addBtn');
        await page.click('#addBtn');
        await page.click('#addBtn');
        // Extract DOM tree after changes
        domTree = await (0, test_helpers_1.extractDomTree)(page);
        const finalElementCount = Object.keys(domTree.map).length;
        // Verify dynamic content was captured
        (0, test_1.expect)(finalElementCount).toBeGreaterThan(initialElementCount);
        const listItems = Object.values(domTree.map).filter((el) => el.tagName === 'li' && el.attributes?.class === 'list-item');
        (0, test_1.expect)(listItems.length).toBe(3);
    });
    (0, test_1.test)('should upload DOM tree to server successfully', async ({ page }) => {
        const testHtml = `
      <div id="upload-test">
        <h1>Upload Test Page</h1>
        <form id="testForm">
          <input type="text" name="username" placeholder="Username">
          <input type="password" name="password" placeholder="Password">
          <button type="submit">Login</button>
        </form>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        // Upload DOM tree to server
        const domId = await (0, api_1.uploadDomTree)(domTree);
        (0, test_1.expect)(domId).toBeTruthy();
        (0, test_1.expect)(typeof domId).toBe('string');
        (0, test_1.expect)(domId.length).toBeGreaterThan(0);
    });
    (0, test_1.test)('should handle large DOM trees efficiently', async ({ page }) => {
        // Generate a large DOM structure
        let largeHtml = '<div id="large-container">';
        for (let i = 0; i < 100; i++) {
            largeHtml += `
        <div class="section-${i}">
          <h3>Section ${i}</h3>
          <p>Content for section ${i}</p>
          <ul>
            ${Array.from({ length: 10 }, (_, j) => `<li>Item ${j}</li>`).join('')}
          </ul>
          <div class="actions">
            <button class="btn edit">Edit</button>
            <button class="btn delete">Delete</button>
          </div>
        </div>
      `;
        }
        largeHtml += '</div>';
        await (0, test_helpers_1.setupTestPage)(page, { html: largeHtml });
        const startTime = Date.now();
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const extractionTime = Date.now() - startTime;
        // Verify large DOM tree was extracted
        (0, test_1.expect)(domTree.rootId).toBeTruthy();
        (0, test_1.expect)(Object.keys(domTree.map).length).toBeGreaterThan(1000);
        // Verify extraction was reasonably fast (less than 5 seconds)
        (0, test_1.expect)(extractionTime).toBeLessThan(5000);
        // Upload large DOM tree
        const uploadStartTime = Date.now();
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const uploadTime = Date.now() - uploadStartTime;
        (0, test_1.expect)(domId).toBeTruthy();
        // Verify upload was reasonably fast (less than 10 seconds)
        (0, test_1.expect)(uploadTime).toBeLessThan(10000);
    });
    (0, test_1.test)('should extract correct element attributes and properties', async ({ page }) => {
        const testHtml = `
      <div id="attributes-test">
        <input id="textInput" type="text" value="default" class="form-control" data-testid="text-input" required>
        <button id="submitBtn" type="submit" class="btn btn-primary" disabled data-action="submit">Submit</button>
        <a id="testLink" href="https://example.com" target="_blank" rel="noopener">External Link</a>
        <img id="testImage" src="test.jpg" alt="Test Image" width="100" height="50">
        <select id="testSelect" multiple>
          <option value="1">Option 1</option>
          <option value="2" selected>Option 2</option>
          <option value="3">Option 3</option>
        </select>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const elements = Object.values(domTree.map);
        // Test input element attributes
        const inputElement = elements.find((el) => el.attributes?.id === 'textInput');
        (0, test_1.expect)(inputElement).toBeTruthy();
        (0, test_1.expect)(inputElement.attributes.type).toBe('text');
        (0, test_1.expect)(inputElement.attributes.class).toBe('form-control');
        (0, test_1.expect)(inputElement.attributes['data-testid']).toBe('text-input');
        (0, test_1.expect)(inputElement.attributes.required).toBe('');
        // Test button element attributes
        const buttonElement = elements.find((el) => el.attributes?.id === 'submitBtn');
        (0, test_1.expect)(buttonElement).toBeTruthy();
        (0, test_1.expect)(buttonElement.attributes.type).toBe('submit');
        (0, test_1.expect)(buttonElement.attributes.class).toBe('btn btn-primary');
        (0, test_1.expect)(buttonElement.attributes.disabled).toBe('');
        // Test link element attributes
        const linkElement = elements.find((el) => el.attributes?.id === 'testLink');
        (0, test_1.expect)(linkElement).toBeTruthy();
        (0, test_1.expect)(linkElement.attributes.href).toBe('https://example.com');
        (0, test_1.expect)(linkElement.attributes.target).toBe('_blank');
        (0, test_1.expect)(linkElement.attributes.rel).toBe('noopener');
        // Test image element attributes
        const imageElement = elements.find((el) => el.attributes?.id === 'testImage');
        (0, test_1.expect)(imageElement).toBeTruthy();
        (0, test_1.expect)(imageElement.attributes.src).toBe('test.jpg');
        (0, test_1.expect)(imageElement.attributes.alt).toBe('Test Image');
        (0, test_1.expect)(imageElement.attributes.width).toBe('100');
        (0, test_1.expect)(imageElement.attributes.height).toBe('50');
    });
});
