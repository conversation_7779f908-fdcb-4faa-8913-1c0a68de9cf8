"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
const api_1 = require("../../api");
const executor_1 = require("../../executor");
test_1.test.describe('Plan Execution Tests', () => {
    test_1.test.beforeEach(async () => {
        await (0, test_helpers_1.verifyServerHealth)();
    });
    (0, test_1.test)('should execute simple button click plan', async ({ page }) => {
        const testHtml = `
      <div id="execution-test">
        <h1>Execution Test Page</h1>
        <button id="clickBtn" onclick="this.textContent='Clicked!'">Click Me</button>
        <div id="result"></div>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('点击按钮', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan with Playwright page context
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 10000 });
        (0, test_1.expect)(executionResult).toBeTruthy();
        (0, test_1.expect)(executionResult.overallStatus).toBe('success');
        (0, test_1.expect)(executionResult.results.length).toBeGreaterThan(0);
        // Verify each step result
        executionResult.results.forEach(result => {
            (0, test_1.expect)(result.step).toBeGreaterThan(0);
            (0, test_1.expect)(result.id).toBeTruthy();
            (0, test_1.expect)(result.status).toBe('success');
            (0, test_1.expect)(result.log).toBeTruthy();
        });
        // Upload execution results
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus, executionResult.errorMessage);
    });
    (0, test_1.test)('should execute form filling plan', async ({ page }) => {
        const testHtml = `
      <div id="form-execution-test">
        <form id="testForm">
          <div>
            <label for="username">Username:</label>
            <input id="username" type="text" name="username">
          </div>
          <div>
            <label for="email">Email:</label>
            <input id="email" type="email" name="email">
          </div>
          <div>
            <label for="message">Message:</label>
            <textarea id="message" name="message"></textarea>
          </div>
          <button type="submit" id="submitBtn">Submit</button>
        </form>
        <div id="formResult"></div>
      </div>
      <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          document.getElementById('formResult').innerHTML = 
            'Form submitted with: ' + Array.from(formData.entries()).map(([k,v]) => k + '=' + v).join(', ');
        });
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('填写表单：用户名"testuser"，邮箱"<EMAIL>"，消息"Hello World"，然后提交', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 15000 });
        (0, test_1.expect)(executionResult.overallStatus).toBe('success');
        // Verify form was filled correctly
        const usernameValue = await page.inputValue('#username');
        const emailValue = await page.inputValue('#email');
        const messageValue = await page.inputValue('#message');
        // Values should be filled (exact values depend on plan generation)
        (0, test_1.expect)(usernameValue.length).toBeGreaterThan(0);
        (0, test_1.expect)(emailValue.length).toBeGreaterThan(0);
        (0, test_1.expect)(messageValue.length).toBeGreaterThan(0);
        // Upload execution results
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus);
    });
    (0, test_1.test)('should handle execution errors gracefully', async ({ page }) => {
        const testHtml = `
      <div id="error-test">
        <button id="validBtn">Valid Button</button>
        <!-- Missing button that might be referenced in plan -->
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('点击一个可能不存在的按钮', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan (might fail)
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 10000 });
        (0, test_1.expect)(executionResult).toBeTruthy();
        (0, test_1.expect)(executionResult.results.length).toBeGreaterThan(0);
        // Check if execution handled errors properly
        if (executionResult.overallStatus === 'failed') {
            (0, test_1.expect)(executionResult.errorMessage).toBeTruthy();
            // Verify failed steps have proper error information
            const failedSteps = executionResult.results.filter(r => r.status === 'failed');
            failedSteps.forEach(step => {
                (0, test_1.expect)(step.log).toBeTruthy();
                (0, test_1.expect)(step.log.length).toBeGreaterThan(0);
            });
        }
        // Upload execution results (including failures)
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus, executionResult.errorMessage);
    });
    (0, test_1.test)('should execute multi-step workflow plan', async ({ page }) => {
        const testHtml = `
      <div id="workflow-test">
        <div id="step1" class="step">
          <h2>Step 1</h2>
          <input id="input1" type="text" placeholder="Enter value 1">
          <button id="next1" onclick="showStep(2)">Next</button>
        </div>
        <div id="step2" class="step" style="display: none;">
          <h2>Step 2</h2>
          <select id="select1">
            <option value="">Choose option</option>
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </select>
          <button id="next2" onclick="showStep(3)">Next</button>
        </div>
        <div id="step3" class="step" style="display: none;">
          <h2>Step 3</h2>
          <p>Review your choices:</p>
          <div id="review"></div>
          <button id="finish" onclick="complete()">Finish</button>
        </div>
        <div id="completion" style="display: none;">
          <h2>Completed!</h2>
          <p>Workflow finished successfully.</p>
        </div>
      </div>
      <script>
        function showStep(stepNum) {
          document.querySelectorAll('.step').forEach(s => s.style.display = 'none');
          document.getElementById('step' + stepNum).style.display = 'block';
          
          if (stepNum === 3) {
            const input1Value = document.getElementById('input1').value;
            const select1Value = document.getElementById('select1').value;
            document.getElementById('review').innerHTML = 
              'Input: ' + input1Value + '<br>Selection: ' + select1Value;
          }
        }
        
        function complete() {
          document.querySelectorAll('.step').forEach(s => s.style.display = 'none');
          document.getElementById('completion').style.display = 'block';
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('完成三步工作流程：第一步输入"test value"，第二步选择"Option 1"，第三步完成流程', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 20000 });
        (0, test_1.expect)(executionResult).toBeTruthy();
        (0, test_1.expect)(executionResult.results.length).toBeGreaterThan(2);
        // Verify workflow progression
        if (executionResult.overallStatus === 'success') {
            // Check if completion message is visible
            const completionVisible = await page.isVisible('#completion');
            if (completionVisible) {
                const completionText = await page.textContent('#completion');
                (0, test_1.expect)(completionText).toContain('Completed');
            }
        }
        // Upload execution results
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus, executionResult.errorMessage);
    });
    (0, test_1.test)('should capture screenshots during execution', async ({ page }) => {
        const testHtml = `
      <div id="screenshot-test">
        <h1>Screenshot Test</h1>
        <button id="changeBtn" onclick="changeContent()">Change Content</button>
        <div id="content">Original Content</div>
      </div>
      <script>
        function changeContent() {
          document.getElementById('content').innerHTML = 'Content Changed!';
          document.getElementById('content').style.color = 'green';
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('点击按钮改变内容', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 10000 });
        (0, test_1.expect)(executionResult).toBeTruthy();
        // Verify screenshots were captured
        executionResult.results.forEach(result => {
            if (result.screenshot) {
                (0, test_1.expect)(result.screenshot).toBeTruthy();
                (0, test_1.expect)(typeof result.screenshot).toBe('string');
                // Should be a base64 encoded image or file path
                (0, test_1.expect)(result.screenshot.length).toBeGreaterThan(0);
            }
        });
        // Upload execution results
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus);
    });
    (0, test_1.test)('should handle different action types correctly', async ({ page }) => {
        const testHtml = `
      <div id="action-types-test">
        <h1>Action Types Test</h1>
        
        <!-- Click/Tap actions -->
        <button id="clickBtn">Click Me</button>
        
        <!-- Input actions -->
        <input id="textInput" type="text" placeholder="Type here">
        
        <!-- Scroll actions -->
        <div id="scrollContainer" style="height: 100px; overflow-y: scroll;">
          <div style="height: 300px;">
            <p>Scroll to see this content</p>
            <p style="margin-top: 200px;">Bottom content</p>
          </div>
        </div>
        
        <!-- Keyboard actions -->
        <input id="keyboardInput" type="text" placeholder="Press keys here">
        
        <!-- Drag actions (if supported) -->
        <div id="dragSource" draggable="true" style="width: 50px; height: 50px; background: blue;">Drag</div>
        <div id="dragTarget" style="width: 100px; height: 100px; border: 2px dashed gray;">Drop here</div>
        
        <div id="results"></div>
      </div>
      <script>
        document.getElementById('clickBtn').onclick = () => {
          document.getElementById('results').innerHTML += 'Button clicked<br>';
        };
        
        document.getElementById('textInput').oninput = (e) => {
          document.getElementById('results').innerHTML += 'Text input: ' + e.target.value + '<br>';
        };
        
        document.getElementById('keyboardInput').onkeydown = (e) => {
          document.getElementById('results').innerHTML += 'Key pressed: ' + e.key + '<br>';
        };
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const domId = await (0, api_1.uploadDomTree)(domTree);
        const plan = await (0, api_1.requestPlan)('点击按钮，在文本框输入"hello"，然后在键盘输入框按回车键', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        // Execute the plan
        const executionResult = await (0, executor_1.executePlan)(plan, { page, timeout: 15000 });
        (0, test_1.expect)(executionResult).toBeTruthy();
        // Verify different action types were executed
        const actionTypes = executionResult.results.map(r => plan.actions.find(a => a.id === r.id)?.type).filter(Boolean);
        // Should contain various action types
        (0, test_1.expect)(actionTypes.length).toBeGreaterThan(0);
        // Upload execution results
        await (0, api_1.uploadResult)(plan.planId, executionResult.results, executionResult.overallStatus, executionResult.errorMessage);
    });
});
