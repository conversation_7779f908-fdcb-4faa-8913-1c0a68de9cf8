"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
test_1.test.describe('Boundary Conditions Tests', () => {
    test_1.test.beforeEach(async () => {
        await (0, test_helpers_1.verifyServerHealth)();
    });
    (0, test_1.test)('should handle empty DOM tree', async ({ page }) => {
        const testHtml = `<!-- Empty page -->`;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        // Should handle empty DOM gracefully
        try {
            const domId = await uploadDomTree(domTree);
            (0, test_1.expect)(domId).toBeTruthy();
            // Try to generate plan for empty DOM
            const plan = await requestPlan('点击按钮', [], { width: 1920, height: 1080 }, domId);
            // Should either generate appropriate plan or handle gracefully
            if (plan) {
                (0, test_1.expect)(plan.actions).toBeTruthy();
            }
        }
        catch (error) {
            // Error handling for empty DOM is acceptable
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle extremely large DOM tree', async ({ page }) => {
        // Generate a very large DOM structure
        let largeHtml = '<div id="large-container">';
        // Create 5000 nested elements
        for (let i = 0; i < 5000; i++) {
            largeHtml += `
        <div class="level-${i % 10}" data-index="${i}">
          <span>Element ${i}</span>
          <input type="text" id="input-${i}" value="value-${i}">
          <button id="btn-${i}" onclick="console.log('${i}')">Button ${i}</button>
        </div>
      `;
        }
        largeHtml += '</div>';
        await (0, test_helpers_1.setupTestPage)(page, { html: largeHtml });
        const startTime = Date.now();
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const extractionTime = Date.now() - startTime;
        // Should handle large DOM within reasonable time
        (0, test_1.expect)(extractionTime).toBeLessThan(30000); // 30 seconds max
        (0, test_1.expect)(Object.keys(domTree.map).length).toBeGreaterThan(10000);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const uploadStartTime = Date.now();
        try {
            const domId = await uploadDomTree(domTree);
            const uploadTime = Date.now() - uploadStartTime;
            (0, test_1.expect)(domId).toBeTruthy();
            (0, test_1.expect)(uploadTime).toBeLessThan(60000); // 60 seconds max for upload
        }
        catch (error) {
            // Large DOM might exceed size limits, which is acceptable
            (0, test_1.expect)(error).toBeTruthy();
            (0, test_1.expect)(error.message).toMatch(/size|limit|too large/i);
        }
    });
    (0, test_1.test)('should handle very long text content', async ({ page }) => {
        const longText = 'A'.repeat(100000); // 100KB of text
        const testHtml = `
      <div id="long-text-test">
        <h1>Long Text Test</h1>
        <p id="longParagraph">${longText}</p>
        <textarea id="longTextarea">${longText}</textarea>
        <input type="text" id="longInput" value="${longText.substring(0, 1000)}">
        <button id="submitBtn">Submit</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            const domId = await uploadDomTree(domTree);
            (0, test_1.expect)(domId).toBeTruthy();
            // Test plan generation with long text content
            const plan = await requestPlan('在长文本区域输入新内容并提交', [], { width: 1920, height: 1080 }, domId);
            if (plan) {
                (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
            }
        }
        catch (error) {
            // Long text might exceed limits
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle special characters and Unicode', async ({ page }) => {
        const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?`~';
        const unicodeChars = '你好世界 🌍 🚀 ñáéíóú αβγδε русский العربية';
        const emojiChars = '😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾';
        const testHtml = `
      <div id="special-chars-test">
        <h1>Special Characters: ${specialChars}</h1>
        <p>Unicode: ${unicodeChars}</p>
        <div>Emojis: ${emojiChars}</div>
        <input type="text" id="specialInput" placeholder="${specialChars}">
        <textarea id="unicodeTextarea">${unicodeChars}</textarea>
        <button id="emojiBtn" title="${emojiChars}">Submit ${emojiChars}</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const domId = await uploadDomTree(domTree);
        (0, test_1.expect)(domId).toBeTruthy();
        // Test plan generation with special characters
        const plan = await requestPlan(`输入特殊字符"${specialChars}"和Unicode文本"${unicodeChars}"`, [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
    });
    (0, test_1.test)('should handle zero and negative dimensions', async ({ page }) => {
        const testHtml = `
      <div id="dimensions-test">
        <div style="width: 0; height: 0;">Zero size element</div>
        <div style="width: -10px; height: -10px;">Negative size element</div>
        <div style="position: absolute; left: -1000px; top: -1000px;">Off-screen element</div>
        <button id="testBtn">Test Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const domId = await uploadDomTree(domTree);
        (0, test_1.expect)(domId).toBeTruthy();
        // Test with zero dimensions
        try {
            const plan = await requestPlan('点击按钮', [], { width: 0, height: 0 }, domId);
            if (plan) {
                (0, test_1.expect)(plan.actions).toBeTruthy();
            }
        }
        catch (error) {
            // Zero dimensions might be rejected
            (0, test_1.expect)(error).toBeTruthy();
        }
        // Test with negative dimensions
        try {
            const plan = await requestPlan('点击按钮', [], { width: -100, height: -100 }, domId);
            if (plan) {
                (0, test_1.expect)(plan.actions).toBeTruthy();
            }
        }
        catch (error) {
            // Negative dimensions might be rejected
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle extremely long instructions', async ({ page }) => {
        const testHtml = `
      <div id="long-instruction-test">
        <button id="btn1">Button 1</button>
        <button id="btn2">Button 2</button>
        <input id="input1" type="text">
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const domId = await uploadDomTree(domTree);
        // Create extremely long instruction (10KB)
        const longInstruction = '请执行以下复杂的操作序列：'.repeat(500) +
            '首先点击按钮1，然后在输入框中输入文本，最后点击按钮2完成操作。';
        try {
            const plan = await requestPlan(longInstruction, [], { width: 1920, height: 1080 }, domId);
            if (plan) {
                (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
            }
        }
        catch (error) {
            // Long instructions might exceed limits
            (0, test_1.expect)(error).toBeTruthy();
            (0, test_1.expect)(error.message).toMatch(/length|limit|too long/i);
        }
    });
    (0, test_1.test)('should handle maximum conversation history', async ({ page }) => {
        const testHtml = `
      <div id="conversation-test">
        <button id="conversationBtn">Conversation Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const domId = await uploadDomTree(domTree);
        // Create very long conversation history (1000 messages)
        const longConversationHistory = [];
        for (let i = 0; i < 1000; i++) {
            longConversationHistory.push({
                role: i % 2 === 0 ? 'user' : 'assistant',
                content: `Message ${i}: This is a test message with some content.`
            });
        }
        try {
            const plan = await requestPlan('点击按钮', longConversationHistory, { width: 1920, height: 1080 }, domId);
            if (plan) {
                (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
            }
        }
        catch (error) {
            // Long conversation history might exceed limits
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle malformed HTML structures', async ({ page }) => {
        const malformedHtml = `
      <div id="malformed-test">
        <p>Unclosed paragraph
        <div>
          <span>Nested without closing</div>
        </span>
        <button onclick="alert('test')" id="malformedBtn">Button</button>
        <input type="text" value="test" unclosed-attribute>
        <!-- Malformed comment --
        <script>console.log('test');</script>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: malformedHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        // Should handle malformed HTML gracefully
        const domId = await uploadDomTree(domTree);
        (0, test_1.expect)(domId).toBeTruthy();
        const plan = await requestPlan('点击按钮', [], { width: 1920, height: 1080 }, domId);
        (0, test_1.expect)(plan).toBeTruthy();
        (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
    });
    (0, test_1.test)('should handle null and undefined values', async ({ page }) => {
        const testHtml = `
      <div id="null-test">
        <button id="nullBtn">Null Test Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const domId = await uploadDomTree(domTree);
        // Test with null/undefined values
        try {
            const plan = await requestPlan(null, [], { width: 1920, height: 1080 }, domId);
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
        }
        try {
            const plan = await requestPlan('点击按钮', null, { width: 1920, height: 1080 }, domId);
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
        }
        try {
            const plan = await requestPlan('点击按钮', [], null, domId);
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle circular references in DOM data', async ({ page }) => {
        const testHtml = `
      <div id="circular-test">
        <button id="circularBtn">Circular Test Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        let domTree = await (0, test_helpers_1.extractDomTree)(page);
        // Artificially create circular reference
        const circularDomTree = {
            rootId: domTree.rootId,
            map: { ...domTree.map }
        };
        // Add circular reference (this would normally be prevented by JSON.stringify)
        // But we test the server's handling of such data
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            const domId = await uploadDomTree(circularDomTree);
            (0, test_1.expect)(domId).toBeTruthy();
        }
        catch (error) {
            // Circular references should be handled gracefully
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
});
