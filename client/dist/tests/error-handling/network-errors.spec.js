"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
test_1.test.describe('Network Error Handling Tests', () => {
    (0, test_1.test)('should handle server unavailable gracefully', async ({ page, context }) => {
        // Block all requests to simulate server down
        await context.route('http://localhost:3000/**', route => {
            route.abort('failed');
        });
        const testHtml = `
      <div id="network-test">
        <button id="testBtn">Test Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        // Import API functions dynamically to test error handling
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        // Test DOM upload with network failure
        try {
            await uploadDomTree(domTree);
            // If it doesn't throw, the retry mechanism might have succeeded
            // which is also valid behavior
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
            (0, test_1.expect)(error.message).toContain('Failed to upload dom tree');
        }
        // Test plan request with network failure
        try {
            await requestPlan('点击按钮', [], { width: 1920, height: 1080 }, 'test-dom-id');
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
            (0, test_1.expect)(error.message).toContain('Failed to generate plan');
        }
    });
    (0, test_1.test)('should handle slow network responses', async ({ page, context }) => {
        // Delay all requests to simulate slow network
        await context.route('http://localhost:3000/**', async (route) => {
            await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
            await route.continue();
        });
        const testHtml = `
      <div id="slow-network-test">
        <button id="slowBtn">Slow Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const startTime = Date.now();
        try {
            const domId = await uploadDomTree(domTree);
            const endTime = Date.now();
            // Should either succeed with delay or timeout
            if (domId) {
                (0, test_1.expect)(domId).toBeTruthy();
                (0, test_1.expect)(endTime - startTime).toBeGreaterThan(4000); // Should take at least 4 seconds due to delay
            }
        }
        catch (error) {
            // Timeout error is acceptable
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle intermittent network failures with retry', async ({ page, context }) => {
        let requestCount = 0;
        // Fail first 2 requests, succeed on 3rd
        await context.route('http://localhost:3000/api/plan/upload/dom', route => {
            requestCount++;
            if (requestCount <= 2) {
                route.abort('failed');
            }
            else {
                route.continue();
            }
        });
        const testHtml = `
      <div id="retry-test">
        <button id="retryBtn">Retry Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        // Should succeed after retries
        const domId = await uploadDomTree(domTree);
        (0, test_1.expect)(domId).toBeTruthy();
        (0, test_1.expect)(requestCount).toBe(3); // Should have made 3 requests
    });
    (0, test_1.test)('should handle HTTP error status codes', async ({ page, context }) => {
        const errorCodes = [400, 401, 403, 404, 500, 502, 503];
        for (const errorCode of errorCodes) {
            // Reset route for each error code
            await context.unroute('http://localhost:3000/**');
            await context.route('http://localhost:3000/**', route => {
                route.fulfill({
                    status: errorCode,
                    contentType: 'application/json',
                    body: JSON.stringify({ error: `HTTP ${errorCode} Error` })
                });
            });
            const testHtml = `
        <div id="error-${errorCode}-test">
          <button id="errorBtn">Error Button</button>
        </div>
      `;
            await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
            const domTree = await (0, test_helpers_1.extractDomTree)(page);
            const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
            try {
                await uploadDomTree(domTree);
                // If it succeeds, the error handling might have recovered
            }
            catch (error) {
                (0, test_1.expect)(error).toBeTruthy();
                (0, test_1.expect)(error.message).toContain(`${errorCode}`);
            }
        }
    });
    (0, test_1.test)('should handle malformed JSON responses', async ({ page, context }) => {
        await context.route('http://localhost:3000/**', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: 'invalid json response'
            });
        });
        const testHtml = `
      <div id="malformed-json-test">
        <button id="jsonBtn">JSON Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            await uploadDomTree(domTree);
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
            // Should handle JSON parsing errors
        }
    });
    (0, test_1.test)('should handle request timeouts', async ({ page, context }) => {
        // Never respond to requests to simulate timeout
        await context.route('http://localhost:3000/**', route => {
            // Don't call route.continue() or route.fulfill() to simulate hanging request
        });
        const testHtml = `
      <div id="timeout-test">
        <button id="timeoutBtn">Timeout Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        const startTime = Date.now();
        try {
            await uploadDomTree(domTree);
        }
        catch (error) {
            const endTime = Date.now();
            (0, test_1.expect)(error).toBeTruthy();
            // Should timeout within reasonable time (API should have timeout configured)
            (0, test_1.expect)(endTime - startTime).toBeLessThan(60000); // Less than 60 seconds
        }
    });
    (0, test_1.test)('should handle CORS errors', async ({ page, context }) => {
        await context.route('http://localhost:3000/**', route => {
            route.fulfill({
                status: 200,
                headers: {
                    'Access-Control-Allow-Origin': 'https://different-origin.com' // Wrong CORS header
                },
                body: JSON.stringify({ success: true })
            });
        });
        const testHtml = `
      <div id="cors-test">
        <button id="corsBtn">CORS Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            await uploadDomTree(domTree);
        }
        catch (error) {
            // CORS errors should be handled gracefully
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle partial response data', async ({ page, context }) => {
        await context.route('http://localhost:3000/api/plan/upload/dom', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    // Missing required domId field
                    success: true,
                    size: 1000
                })
            });
        });
        const testHtml = `
      <div id="partial-response-test">
        <button id="partialBtn">Partial Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            const domId = await uploadDomTree(domTree);
            // Should handle missing domId gracefully
            if (domId) {
                (0, test_1.expect)(typeof domId).toBe('string');
            }
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
        }
    });
    (0, test_1.test)('should handle network disconnection during request', async ({ page, context }) => {
        let disconnected = false;
        await context.route('http://localhost:3000/**', route => {
            if (!disconnected) {
                // Simulate network disconnection after first request starts
                disconnected = true;
                setTimeout(() => route.abort('failed'), 1000);
            }
            else {
                route.abort('failed');
            }
        });
        const testHtml = `
      <div id="disconnection-test">
        <button id="disconnectBtn">Disconnect Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree } = await Promise.resolve().then(() => __importStar(require('../../api')));
        try {
            await uploadDomTree(domTree);
        }
        catch (error) {
            (0, test_1.expect)(error).toBeTruthy();
            (0, test_1.expect)(error.message).toContain('Failed to upload dom tree');
        }
    });
    (0, test_1.test)('should handle concurrent network requests with failures', async ({ page, context }) => {
        let requestCount = 0;
        await context.route('http://localhost:3000/**', route => {
            requestCount++;
            // Fail every other request
            if (requestCount % 2 === 0) {
                route.abort('failed');
            }
            else {
                route.continue();
            }
        });
        const testHtml = `
      <div id="concurrent-test">
        <button id="concurrentBtn">Concurrent Button</button>
      </div>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: testHtml });
        const domTree = await (0, test_helpers_1.extractDomTree)(page);
        const { uploadDomTree, requestPlan } = await Promise.resolve().then(() => __importStar(require('../../api')));
        // Make multiple concurrent requests
        const promises = [
            uploadDomTree(domTree),
            uploadDomTree(domTree),
            uploadDomTree(domTree)
        ];
        const results = await Promise.allSettled(promises);
        // At least some requests should succeed or fail gracefully
        results.forEach(result => {
            if (result.status === 'fulfilled') {
                (0, test_1.expect)(result.value).toBeTruthy();
            }
            else {
                (0, test_1.expect)(result.reason).toBeTruthy();
            }
        });
    });
});
