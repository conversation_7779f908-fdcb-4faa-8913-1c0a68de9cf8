"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const util_1 = require("util");
const path_1 = __importDefault(require("path"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
/**
 * Global teardown for E2E tests
 * Cleans up test environment and resources
 */
async function globalTeardown(config) {
    console.log('🧹 Starting global teardown for Browser RPA E2E tests...');
    try {
        // 1. Close browser context
        if (global.testContext) {
            console.log('🌐 Closing test browser context...');
            await global.testContext.close();
        }
        if (global.testBrowser) {
            console.log('🌐 Closing test browser...');
            await global.testBrowser.close();
        }
        // 2. Clean up test data
        console.log('🗑️ Cleaning up test data...');
        await cleanupTestData();
        // 3. Generate test report summary
        console.log('📊 Generating test report summary...');
        await generateTestSummary();
        console.log('✅ Global teardown completed successfully');
    }
    catch (error) {
        console.error('❌ Global teardown failed:', error);
        // Don't throw error to avoid masking test failures
    }
}
/**
 * Clean up test data
 */
async function cleanupTestData() {
    try {
        // Clean up test sessions and data
        const response = await fetch('http://localhost:3000/api/cleanup/run', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                testCleanup: true,
                sessionIds: ['test-session-001']
            })
        });
        if (response.ok) {
            console.log('✅ Test data cleaned up successfully');
        }
        else {
            console.warn('⚠️ Failed to clean up test data');
        }
    }
    catch (error) {
        console.warn('⚠️ Error during test data cleanup:', error);
    }
}
/**
 * Generate test summary
 */
async function generateTestSummary() {
    try {
        const resultsPath = path_1.default.resolve(__dirname, '../../../test-results');
        // Read test results if available
        const fs = require('fs');
        const resultsFile = path_1.default.join(resultsPath, 'results.json');
        if (fs.existsSync(resultsFile)) {
            const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
            console.log('\n📋 Test Summary:');
            console.log(`   Total tests: ${results.stats?.total || 'N/A'}`);
            console.log(`   Passed: ${results.stats?.passed || 'N/A'}`);
            console.log(`   Failed: ${results.stats?.failed || 'N/A'}`);
            console.log(`   Skipped: ${results.stats?.skipped || 'N/A'}`);
            console.log(`   Duration: ${results.stats?.duration || 'N/A'}ms`);
        }
    }
    catch (error) {
        console.warn('⚠️ Failed to generate test summary:', error);
    }
}
exports.default = globalTeardown;
