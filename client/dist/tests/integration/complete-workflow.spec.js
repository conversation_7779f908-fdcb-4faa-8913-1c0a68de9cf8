"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const test_helpers_1 = require("../utils/test-helpers");
test_1.test.describe('Complete RPA Workflow Integration Tests', () => {
    test_1.test.beforeEach(async () => {
        await (0, test_helpers_1.verifyServerHealth)();
    });
    (0, test_1.test)('should complete full e-commerce workflow', async ({ page }) => {
        const ecommerceHtml = `
      <div id="ecommerce-app">
        <header>
          <nav>
            <a href="#products" id="productsLink">Products</a>
            <a href="#cart" id="cartLink">Cart (<span id="cartCount">0</span>)</a>
          </nav>
        </header>
        
        <main>
          <section id="products">
            <h1>Products</h1>
            <div class="product-grid">
              <div class="product" data-id="1">
                <h3>Laptop</h3>
                <p class="price">$999</p>
                <button class="add-to-cart" data-product="1">Add to Cart</button>
              </div>
              <div class="product" data-id="2">
                <h3>Mouse</h3>
                <p class="price">$29</p>
                <button class="add-to-cart" data-product="2">Add to Cart</button>
              </div>
              <div class="product" data-id="3">
                <h3>Keyboard</h3>
                <p class="price">$79</p>
                <button class="add-to-cart" data-product="3">Add to Cart</button>
              </div>
            </div>
          </section>
          
          <section id="cart" style="display: none;">
            <h1>Shopping Cart</h1>
            <div id="cartItems"></div>
            <div id="cartTotal">Total: $0</div>
            <button id="checkoutBtn" style="display: none;">Proceed to Checkout</button>
          </section>
          
          <section id="checkout" style="display: none;">
            <h1>Checkout</h1>
            <form id="checkoutForm">
              <div>
                <label for="customerName">Name:</label>
                <input type="text" id="customerName" name="name" required>
              </div>
              <div>
                <label for="customerEmail">Email:</label>
                <input type="email" id="customerEmail" name="email" required>
              </div>
              <div>
                <label for="customerAddress">Address:</label>
                <textarea id="customerAddress" name="address" required></textarea>
              </div>
              <button type="submit" id="placeOrderBtn">Place Order</button>
            </form>
          </section>
          
          <section id="confirmation" style="display: none;">
            <h1>Order Confirmed!</h1>
            <p>Thank you for your order. Your order number is <span id="orderNumber"></span></p>
          </section>
        </main>
      </div>
      
      <script>
        let cart = [];
        let cartTotal = 0;
        
        // Add to cart functionality
        document.querySelectorAll('.add-to-cart').forEach(btn => {
          btn.addEventListener('click', function() {
            const productId = this.dataset.product;
            const productElement = this.closest('.product');
            const name = productElement.querySelector('h3').textContent;
            const price = parseFloat(productElement.querySelector('.price').textContent.replace('$', ''));
            
            cart.push({ id: productId, name, price });
            cartTotal += price;
            
            updateCartDisplay();
            this.textContent = 'Added!';
            this.disabled = true;
          });
        });
        
        // Cart link functionality
        document.getElementById('cartLink').addEventListener('click', function(e) {
          e.preventDefault();
          showSection('cart');
        });
        
        // Products link functionality
        document.getElementById('productsLink').addEventListener('click', function(e) {
          e.preventDefault();
          showSection('products');
        });
        
        // Checkout button functionality
        document.getElementById('checkoutBtn').addEventListener('click', function() {
          showSection('checkout');
        });
        
        // Checkout form functionality
        document.getElementById('checkoutForm').addEventListener('submit', function(e) {
          e.preventDefault();
          const orderNumber = 'ORD-' + Math.random().toString(36).substr(2, 9).toUpperCase();
          document.getElementById('orderNumber').textContent = orderNumber;
          showSection('confirmation');
        });
        
        function updateCartDisplay() {
          document.getElementById('cartCount').textContent = cart.length;
          
          const cartItems = document.getElementById('cartItems');
          cartItems.innerHTML = '';
          
          cart.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.innerHTML = item.name + ' - $' + item.price;
            cartItems.appendChild(itemDiv);
          });
          
          document.getElementById('cartTotal').textContent = 'Total: $' + cartTotal;
          
          if (cart.length > 0) {
            document.getElementById('checkoutBtn').style.display = 'block';
          }
        }
        
        function showSection(sectionName) {
          document.querySelectorAll('section').forEach(s => s.style.display = 'none');
          document.getElementById(sectionName).style.display = 'block';
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: ecommerceHtml });
        // Execute complete e-commerce workflow
        const workflow = await (0, test_helpers_1.executeRPAWorkflow)(page, '购买一台笔记本电脑和一个鼠标，然后填写客户信息完成订单：姓名"John Doe"，邮箱"<EMAIL>"，地址"123 Main St, City, State"', 'ecommerce-session');
        (0, test_1.expect)(workflow.plan.actions.length).toBeGreaterThan(5);
        (0, test_1.expect)(workflow.executionResults.overallStatus).toBe('success');
        // Verify the workflow completed successfully by checking final state
        // (In a real test, you might execute the plan with Playwright)
    });
    (0, test_1.test)('should complete user registration and profile workflow', async ({ page }) => {
        const registrationHtml = `
      <div id="registration-app">
        <div id="loginSection">
          <h1>Login</h1>
          <form id="loginForm">
            <input type="email" id="loginEmail" placeholder="Email" required>
            <input type="password" id="loginPassword" placeholder="Password" required>
            <button type="submit">Login</button>
          </form>
          <p><a href="#" id="registerLink">Don't have an account? Register</a></p>
        </div>
        
        <div id="registerSection" style="display: none;">
          <h1>Register</h1>
          <form id="registerForm">
            <input type="text" id="firstName" placeholder="First Name" required>
            <input type="text" id="lastName" placeholder="Last Name" required>
            <input type="email" id="registerEmail" placeholder="Email" required>
            <input type="password" id="registerPassword" placeholder="Password" required>
            <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
            <button type="submit">Register</button>
          </form>
          <p><a href="#" id="loginLink">Already have an account? Login</a></p>
        </div>
        
        <div id="profileSection" style="display: none;">
          <h1>Profile</h1>
          <div id="profileInfo">
            <p>Welcome, <span id="userName"></span>!</p>
            <button id="editProfileBtn">Edit Profile</button>
            <button id="logoutBtn">Logout</button>
          </div>
          
          <div id="editProfileForm" style="display: none;">
            <h2>Edit Profile</h2>
            <form id="profileForm">
              <input type="text" id="editFirstName" placeholder="First Name">
              <input type="text" id="editLastName" placeholder="Last Name">
              <input type="text" id="editPhone" placeholder="Phone Number">
              <textarea id="editBio" placeholder="Bio"></textarea>
              <button type="submit">Save Changes</button>
              <button type="button" id="cancelEditBtn">Cancel</button>
            </form>
          </div>
        </div>
        
        <div id="successMessage" style="display: none;">
          <h2>Success!</h2>
          <p id="successText"></p>
        </div>
      </div>
      
      <script>
        let currentUser = null;
        
        // Navigation
        document.getElementById('registerLink').addEventListener('click', function(e) {
          e.preventDefault();
          showSection('registerSection');
        });
        
        document.getElementById('loginLink').addEventListener('click', function(e) {
          e.preventDefault();
          showSection('loginSection');
        });
        
        // Registration
        document.getElementById('registerForm').addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          const userData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            email: formData.get('registerEmail'),
            password: formData.get('registerPassword')
          };
          
          // Simulate registration
          currentUser = userData;
          document.getElementById('userName').textContent = userData.firstName + ' ' + userData.lastName;
          showSection('profileSection');
          showSuccess('Registration successful! Welcome to our platform.');
        });
        
        // Login
        document.getElementById('loginForm').addEventListener('submit', function(e) {
          e.preventDefault();
          // Simulate login
          if (currentUser) {
            document.getElementById('userName').textContent = currentUser.firstName + ' ' + currentUser.lastName;
            showSection('profileSection');
            showSuccess('Login successful! Welcome back.');
          }
        });
        
        // Profile editing
        document.getElementById('editProfileBtn').addEventListener('click', function() {
          document.getElementById('editProfileForm').style.display = 'block';
          if (currentUser) {
            document.getElementById('editFirstName').value = currentUser.firstName;
            document.getElementById('editLastName').value = currentUser.lastName;
          }
        });
        
        document.getElementById('cancelEditBtn').addEventListener('click', function() {
          document.getElementById('editProfileForm').style.display = 'none';
        });
        
        document.getElementById('profileForm').addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          
          // Update user data
          if (currentUser) {
            currentUser.firstName = formData.get('editFirstName') || currentUser.firstName;
            currentUser.lastName = formData.get('editLastName') || currentUser.lastName;
            currentUser.phone = formData.get('editPhone');
            currentUser.bio = formData.get('editBio');
            
            document.getElementById('userName').textContent = currentUser.firstName + ' ' + currentUser.lastName;
          }
          
          document.getElementById('editProfileForm').style.display = 'none';
          showSuccess('Profile updated successfully!');
        });
        
        // Logout
        document.getElementById('logoutBtn').addEventListener('click', function() {
          currentUser = null;
          showSection('loginSection');
          showSuccess('Logged out successfully!');
        });
        
        function showSection(sectionId) {
          document.querySelectorAll('#registration-app > div').forEach(div => {
            if (div.id === 'successMessage') return;
            div.style.display = 'none';
          });
          document.getElementById(sectionId).style.display = 'block';
        }
        
        function showSuccess(message) {
          document.getElementById('successText').textContent = message;
          document.getElementById('successMessage').style.display = 'block';
          setTimeout(() => {
            document.getElementById('successMessage').style.display = 'none';
          }, 3000);
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: registrationHtml });
        // Execute user registration workflow
        const workflow = await (0, test_helpers_1.executeRPAWorkflow)(page, '注册新用户：姓名"Jane Smith"，邮箱"<EMAIL>"，密码"password123"，然后编辑个人资料添加电话号码"555-1234"和简介"Software Developer"', 'registration-session');
        (0, test_1.expect)(workflow.plan.actions.length).toBeGreaterThan(7);
        (0, test_1.expect)(workflow.executionResults.overallStatus).toBe('success');
    });
    (0, test_1.test)('should complete data entry and validation workflow', async ({ page }) => {
        const dataEntryHtml = `
      <div id="data-entry-app">
        <h1>Employee Data Entry</h1>
        <form id="employeeForm">
          <div class="form-section">
            <h2>Personal Information</h2>
            <div class="form-row">
              <input type="text" id="empFirstName" placeholder="First Name" required>
              <input type="text" id="empLastName" placeholder="Last Name" required>
            </div>
            <div class="form-row">
              <input type="email" id="empEmail" placeholder="Email" required>
              <input type="tel" id="empPhone" placeholder="Phone" required>
            </div>
            <div class="form-row">
              <input type="date" id="empBirthDate" required>
              <select id="empGender" required>
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          
          <div class="form-section">
            <h2>Employment Details</h2>
            <div class="form-row">
              <input type="text" id="empId" placeholder="Employee ID" required>
              <select id="empDepartment" required>
                <option value="">Select Department</option>
                <option value="engineering">Engineering</option>
                <option value="marketing">Marketing</option>
                <option value="sales">Sales</option>
                <option value="hr">Human Resources</option>
              </select>
            </div>
            <div class="form-row">
              <input type="text" id="empPosition" placeholder="Position" required>
              <input type="number" id="empSalary" placeholder="Salary" required>
            </div>
            <div class="form-row">
              <input type="date" id="empStartDate" required>
              <select id="empStatus" required>
                <option value="">Employment Status</option>
                <option value="full-time">Full Time</option>
                <option value="part-time">Part Time</option>
                <option value="contract">Contract</option>
              </select>
            </div>
          </div>
          
          <div class="form-section">
            <h2>Additional Information</h2>
            <div class="form-row">
              <textarea id="empNotes" placeholder="Additional Notes"></textarea>
            </div>
            <div class="form-row">
              <label>
                <input type="checkbox" id="empAgreement" required>
                I agree to the terms and conditions
              </label>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" id="validateBtn">Validate Data</button>
            <button type="submit" id="submitBtn" disabled>Submit</button>
            <button type="reset" id="resetBtn">Reset Form</button>
          </div>
        </form>
        
        <div id="validationResults" style="display: none;">
          <h2>Validation Results</h2>
          <div id="validationMessages"></div>
        </div>
        
        <div id="submissionSuccess" style="display: none;">
          <h2>Success!</h2>
          <p>Employee data has been successfully submitted.</p>
          <p>Employee ID: <span id="submittedEmpId"></span></p>
        </div>
      </div>
      
      <script>
        document.getElementById('validateBtn').addEventListener('click', function() {
          validateForm();
        });
        
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
          e.preventDefault();
          if (validateForm()) {
            submitForm();
          }
        });
        
        function validateForm() {
          const messages = [];
          let isValid = true;
          
          // Validate required fields
          const requiredFields = [
            'empFirstName', 'empLastName', 'empEmail', 'empPhone',
            'empBirthDate', 'empGender', 'empId', 'empDepartment',
            'empPosition', 'empSalary', 'empStartDate', 'empStatus'
          ];
          
          requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
              messages.push(field.placeholder + ' is required');
              isValid = false;
            }
          });
          
          // Validate email format
          const email = document.getElementById('empEmail').value;
          if (email && !email.includes('@')) {
            messages.push('Invalid email format');
            isValid = false;
          }
          
          // Validate salary is positive
          const salary = document.getElementById('empSalary').value;
          if (salary && parseFloat(salary) <= 0) {
            messages.push('Salary must be positive');
            isValid = false;
          }
          
          // Validate agreement checkbox
          if (!document.getElementById('empAgreement').checked) {
            messages.push('You must agree to the terms and conditions');
            isValid = false;
          }
          
          // Display validation results
          const resultsDiv = document.getElementById('validationResults');
          const messagesDiv = document.getElementById('validationMessages');
          
          if (messages.length > 0) {
            messagesDiv.innerHTML = messages.map(msg => '<p style="color: red;">• ' + msg + '</p>').join('');
            resultsDiv.style.display = 'block';
          } else {
            messagesDiv.innerHTML = '<p style="color: green;">All validations passed!</p>';
            resultsDiv.style.display = 'block';
            document.getElementById('submitBtn').disabled = false;
          }
          
          return isValid;
        }
        
        function submitForm() {
          const empId = document.getElementById('empId').value;
          document.getElementById('submittedEmpId').textContent = empId;
          document.getElementById('submissionSuccess').style.display = 'block';
          document.getElementById('employeeForm').style.display = 'none';
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: dataEntryHtml });
        // Execute data entry workflow
        const workflow = await (0, test_helpers_1.executeRPAWorkflow)(page, '填写员工信息表单：姓名"Alice Johnson"，邮箱"<EMAIL>"，电话"555-0123"，生日"1990-05-15"，性别"female"，员工ID"EMP001"，部门"engineering"，职位"Software Engineer"，薪资"75000"，入职日期"2024-01-15"，状态"full-time"，备注"Experienced developer"，然后验证数据并提交', 'data-entry-session');
        (0, test_1.expect)(workflow.plan.actions.length).toBeGreaterThan(15);
        (0, test_1.expect)(workflow.executionResults.overallStatus).toBe('success');
    });
    (0, test_1.test)('should handle complex multi-page navigation workflow', async ({ page }) => {
        const multiPageHtml = `
      <div id="multi-page-app">
        <nav id="mainNav">
          <a href="#" data-page="home" class="nav-link active">Home</a>
          <a href="#" data-page="products" class="nav-link">Products</a>
          <a href="#" data-page="services" class="nav-link">Services</a>
          <a href="#" data-page="contact" class="nav-link">Contact</a>
        </nav>
        
        <div id="homePage" class="page">
          <h1>Welcome to Our Website</h1>
          <p>This is the home page.</p>
          <button id="getStartedBtn">Get Started</button>
        </div>
        
        <div id="productsPage" class="page" style="display: none;">
          <h1>Our Products</h1>
          <div class="product-list">
            <div class="product-item">
              <h3>Product A</h3>
              <button class="view-details" data-product="a">View Details</button>
            </div>
            <div class="product-item">
              <h3>Product B</h3>
              <button class="view-details" data-product="b">View Details</button>
            </div>
          </div>
        </div>
        
        <div id="servicesPage" class="page" style="display: none;">
          <h1>Our Services</h1>
          <div class="service-list">
            <div class="service-item">
              <h3>Consulting</h3>
              <button class="request-service" data-service="consulting">Request Quote</button>
            </div>
            <div class="service-item">
              <h3>Development</h3>
              <button class="request-service" data-service="development">Request Quote</button>
            </div>
          </div>
        </div>
        
        <div id="contactPage" class="page" style="display: none;">
          <h1>Contact Us</h1>
          <form id="contactForm">
            <input type="text" id="contactName" placeholder="Your Name" required>
            <input type="email" id="contactEmail" placeholder="Your Email" required>
            <select id="contactSubject" required>
              <option value="">Select Subject</option>
              <option value="general">General Inquiry</option>
              <option value="support">Support</option>
              <option value="sales">Sales</option>
            </select>
            <textarea id="contactMessage" placeholder="Your Message" required></textarea>
            <button type="submit">Send Message</button>
          </form>
        </div>
        
        <div id="productDetailsModal" class="modal" style="display: none;">
          <div class="modal-content">
            <h2 id="productTitle">Product Details</h2>
            <p id="productDescription">Product description goes here.</p>
            <button id="closeModal">Close</button>
            <button id="addToCartModal">Add to Cart</button>
          </div>
        </div>
        
        <div id="quoteModal" class="modal" style="display: none;">
          <div class="modal-content">
            <h2>Request Quote</h2>
            <form id="quoteForm">
              <input type="text" id="quoteName" placeholder="Your Name" required>
              <input type="email" id="quoteEmail" placeholder="Your Email" required>
              <textarea id="quoteRequirements" placeholder="Project Requirements" required></textarea>
              <button type="submit">Submit Request</button>
              <button type="button" id="closeQuoteModal">Cancel</button>
            </form>
          </div>
        </div>
      </div>
      
      <script>
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetPage = this.dataset.page;
            showPage(targetPage);
            
            // Update active nav
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
          });
        });
        
        // Get started button
        document.getElementById('getStartedBtn').addEventListener('click', function() {
          showPage('products');
          document.querySelector('[data-page="products"]').classList.add('active');
          document.querySelector('[data-page="home"]').classList.remove('active');
        });
        
        // Product details
        document.querySelectorAll('.view-details').forEach(btn => {
          btn.addEventListener('click', function() {
            const product = this.dataset.product;
            document.getElementById('productTitle').textContent = 'Product ' + product.toUpperCase();
            document.getElementById('productDescription').textContent = 'Detailed information about Product ' + product.toUpperCase();
            document.getElementById('productDetailsModal').style.display = 'block';
          });
        });
        
        // Service requests
        document.querySelectorAll('.request-service').forEach(btn => {
          btn.addEventListener('click', function() {
            document.getElementById('quoteModal').style.display = 'block';
          });
        });
        
        // Modal controls
        document.getElementById('closeModal').addEventListener('click', function() {
          document.getElementById('productDetailsModal').style.display = 'none';
        });
        
        document.getElementById('closeQuoteModal').addEventListener('click', function() {
          document.getElementById('quoteModal').style.display = 'none';
        });
        
        // Form submissions
        document.getElementById('contactForm').addEventListener('submit', function(e) {
          e.preventDefault();
          alert('Contact form submitted successfully!');
        });
        
        document.getElementById('quoteForm').addEventListener('submit', function(e) {
          e.preventDefault();
          alert('Quote request submitted successfully!');
          document.getElementById('quoteModal').style.display = 'none';
        });
        
        function showPage(pageId) {
          document.querySelectorAll('.page').forEach(page => page.style.display = 'none');
          document.getElementById(pageId + 'Page').style.display = 'block';
        }
      </script>
    `;
        await (0, test_helpers_1.setupTestPage)(page, { html: multiPageHtml });
        // Execute multi-page navigation workflow
        const workflow = await (0, test_helpers_1.executeRPAWorkflow)(page, '浏览网站：首先查看产品页面并查看Product A的详细信息，然后转到服务页面请求开发服务报价（姓名"Bob Wilson"，邮箱"<EMAIL>"，需求"Need a web application"），最后到联系页面发送消息（姓名"Bob Wilson"，邮箱"<EMAIL>"，主题"general"，消息"Interested in your services"）', 'navigation-session');
        (0, test_1.expect)(workflow.plan.actions.length).toBeGreaterThan(10);
        (0, test_1.expect)(workflow.executionResults.overallStatus).toBe('success');
    });
});
