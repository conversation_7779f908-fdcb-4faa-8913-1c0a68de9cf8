"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTestPage = setupTestPage;
exports.extractDomTree = extractDomTree;
exports.executeRPAWorkflow = executeRPAWorkflow;
exports.waitForApiResponse = waitForApiResponse;
exports.verifyServerHealth = verifyServerHealth;
const test_1 = require("@playwright/test");
const api_1 = require("../../api");
/**
 * Setup a test page with custom HTML or navigate to URL
 */
async function setupTestPage(page, config) {
    if (config.viewport) {
        await page.setViewportSize(config.viewport);
    }
    if (config.html) {
        // Create a test page with custom HTML
        const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Test Page</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
            .input { padding: 8px; margin: 5px; border: 1px solid #ccc; }
            .hidden { display: none; }
            .disabled { opacity: 0.5; pointer-events: none; }
          </style>
        </head>
        <body>
          ${config.html}
        </body>
      </html>
    `;
        await page.setContent(htmlContent);
    }
    else if (config.url) {
        await page.goto(config.url);
    }
    if (config.waitForSelector) {
        await page.waitForSelector(config.waitForSelector);
    }
    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle');
}
/**
 * Extract DOM tree from current page
 */
async function extractDomTree(page) {
    return await page.evaluate(() => {
        // Inline DOM tree builder for page context
        function buildDomTreeMap() {
            const map = {};
            let idCounter = 0;
            function traverse(node) {
                if (!node)
                    return null;
                const id = `${idCounter++}`;
                if (node.nodeType === Node.TEXT_NODE) {
                    const text = node.textContent?.trim();
                    if (!text)
                        return null;
                    map[id] = {
                        type: 'TEXT_NODE',
                        text,
                        isVisible: true,
                    };
                    return id;
                }
                if (node.nodeType !== Node.ELEMENT_NODE)
                    return null;
                const el = node;
                const rect = el.getBoundingClientRect?.() || {
                    x: 0, y: 0, width: 0, height: 0,
                };
                const childrenIds = [];
                for (const child of Array.from(el.childNodes)) {
                    const childId = traverse(child);
                    if (childId)
                        childrenIds.push(childId);
                }
                function getXPath(element) {
                    if (element.id)
                        return `//*[@id="${element.id}"]`;
                    const parts = [];
                    while (element && element.nodeType === Node.ELEMENT_NODE) {
                        let index = 1;
                        let sibling = element.previousElementSibling;
                        while (sibling) {
                            if (sibling.tagName === element.tagName)
                                index++;
                            sibling = sibling.previousElementSibling;
                        }
                        parts.unshift(`${element.tagName.toLowerCase()}[${index}]`);
                        element = element.parentElement;
                    }
                    return `/${parts.join('/')}`;
                }
                map[id] = {
                    tagName: el.tagName.toLowerCase(),
                    attributes: Object.fromEntries(Array.from(el.attributes).map((attr) => [attr.name, attr.value])),
                    xpath: getXPath(el),
                    children: childrenIds,
                    bbox: {
                        x: rect.x || 0,
                        y: rect.y || 0,
                        width: rect.width || 0,
                        height: rect.height || 0,
                    },
                    isVisible: true,
                    isTopElement: true,
                    isInteractive: false,
                    highlightIndex: -1,
                };
                return id;
            }
            const rootId = traverse(document.body);
            return { rootId: rootId || '', map };
        }
        return buildDomTreeMap();
    });
}
/**
 * Execute complete RPA workflow: DOM extraction -> Plan generation -> Execution
 */
async function executeRPAWorkflow(page, userInstruction, sessionId = 'test-session') {
    // 1. Extract DOM tree
    const domTree = await extractDomTree(page);
    (0, test_1.expect)(domTree.rootId).toBeTruthy();
    (0, test_1.expect)(Object.keys(domTree.map).length).toBeGreaterThan(0);
    // 2. Upload DOM tree
    const domId = await (0, api_1.uploadDomTree)(domTree);
    (0, test_1.expect)(domId).toBeTruthy();
    // 3. Generate plan
    const plan = await (0, api_1.requestPlan)(userInstruction, [], { width: 1920, height: 1080 }, domId, undefined, sessionId);
    (0, test_1.expect)(plan).toBeTruthy();
    (0, test_1.expect)(plan.actions.length).toBeGreaterThan(0);
    // 4. Execute plan (mock execution for now)
    const executionResults = await mockExecutePlan(plan);
    // 5. Upload results
    await (0, api_1.uploadResult)(plan.planId, executionResults.results, executionResults.overallStatus, executionResults.errorMessage);
    return {
        domId,
        plan,
        executionResults
    };
}
/**
 * Mock plan execution for testing
 */
async function mockExecutePlan(plan) {
    const results = [];
    for (let i = 0; i < plan.actions.length; i++) {
        const action = plan.actions[i];
        results.push({
            step: i + 1,
            id: action.id,
            status: 'success',
            screenshot: 'data:image/png;base64,mock-screenshot',
            log: `Successfully executed ${action.type}`,
        });
    }
    return {
        results,
        overallStatus: 'success'
    };
}
/**
 * Wait for API response with timeout
 */
async function waitForApiResponse(apiCall, timeout = 30000) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
        try {
            return await apiCall();
        }
        catch (error) {
            if (Date.now() - startTime >= timeout) {
                throw new Error(`API call timed out after ${timeout}ms: ${error}`);
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
}
/**
 * Verify server health
 */
async function verifyServerHealth() {
    const response = await fetch('http://localhost:3000/api/plan/health');
    (0, test_1.expect)(response.ok).toBeTruthy();
    const health = await response.json();
    (0, test_1.expect)(health.status).toBe('healthy');
}
