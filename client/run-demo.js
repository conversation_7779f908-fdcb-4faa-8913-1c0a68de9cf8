/**
 * 运行百度搜索演示的简单脚本
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 准备运行百度搜索RPA演示...');
console.log('📋 任务: "打开百度主页，搜索如何注册stripe，并获取前三个结果"');
console.log('=' .repeat(60));

try {
  // 检查是否安装了依赖
  console.log('🔍 检查依赖...');
  
  try {
    require('playwright');
    console.log('✅ Playwright 已安装');
  } catch (error) {
    console.log('❌ Playwright 未安装，正在安装...');
    execSync('npm install playwright', { stdio: 'inherit' });
    console.log('✅ Playwright 安装完成');
  }

  // 检查浏览器
  console.log('🌐 检查浏览器...');
  try {
    execSync('npx playwright install chromium', { stdio: 'inherit' });
    console.log('✅ 浏览器准备完成');
  } catch (error) {
    console.log('⚠️ 浏览器安装可能有问题，但继续尝试运行...');
  }

  // 编译TypeScript并运行
  console.log('⚙️ 编译并运行演示...');
  
  const demoPath = path.join(__dirname, 'src', 'demo-baidu-search.ts');
  
  // 使用ts-node运行TypeScript文件
  try {
    execSync(`npx ts-node "${demoPath}"`, { 
      stdio: 'inherit',
      cwd: __dirname 
    });
  } catch (error) {
    console.log('⚠️ ts-node 运行失败，尝试先编译...');
    
    // 编译TypeScript
    execSync('npx tsc', { stdio: 'inherit', cwd: __dirname });
    
    // 运行编译后的JavaScript
    const jsPath = demoPath.replace('.ts', '.js').replace('src/', 'dist/');
    execSync(`node "${jsPath}"`, { 
      stdio: 'inherit',
      cwd: __dirname 
    });
  }

} catch (error) {
  console.error('❌ 运行演示时发生错误:', error.message);
  console.log('\n📝 手动运行步骤:');
  console.log('1. 确保安装了 Node.js 和 npm');
  console.log('2. 运行: npm install');
  console.log('3. 运行: npm install playwright');
  console.log('4. 运行: npx playwright install');
  console.log('5. 运行: npx ts-node src/demo-baidu-search.ts');
}

console.log('\n🎯 演示说明:');
console.log('- 这个演示展示了Browser RPA系统如何自动化执行复杂的网页操作');
console.log('- 系统会自动分析页面结构，生成执行计划，然后执行搜索操作');
console.log('- 演示包括: DOM分析 → 计划生成 → 自动执行 → 结果提取');
console.log('- 浏览器窗口会显示整个自动化过程');
