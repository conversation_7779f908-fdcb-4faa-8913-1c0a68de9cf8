export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface Size {
  width: number;
  height: number;
}

export interface BBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface LocateInfo {
  prompt: string;
  bbox: BBox;
}

export interface PlanningAction {
  id: string;
  type: string;
  locate?: LocateInfo | null;
  param?: any;
  thought?: string;
}

export interface GeneratePlanResponse {
  planId: string;
  actions: PlanningAction[];
  action_summary: string;
  screenshotUrl?: string;
}

export interface StepResult {
  step: number;
  id?: string;
  status: 'success' | 'failed';
  screenshot?: string;
  log?: string;
}

import { RetryHand<PERSON>, TimeoutHandler, CircuitBreaker } from './retry-handler';

const SERVER_URL = 'http://localhost:3000/api/plan';

// Initialize error handling components
const retryHandler = new RetryHandler();
const planCircuitBreaker = new CircuitBreaker(3, 30000); // 3 failures, 30s recovery
const uploadCircuitBreaker = new CircuitBreaker(3, 30000);

/**
 * 请求生成Plan - with retry and circuit breaker
 */
export async function requestPlan(
  userInstruction: string,
  conversationHistory: ConversationMessage[],
  size: Size,
  domId?: string,
  screenshotUrl?: string,
): Promise<GeneratePlanResponse> {
  return planCircuitBreaker.execute(async () => {
    return retryHandler.executeWithRetry(async () => {
      return TimeoutHandler.withTimeout(async () => {
        const resp = await fetch(`${SERVER_URL}/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userInstruction,
            conversationHistory,
            size,
            domId,
            screenshotUrl,
          }),
        });
        
        if (!resp.ok) {
          const errorText = await resp.text();
          throw new Error(`Failed to generate plan: ${resp.status} ${errorText}`);
        }
        
        return resp.json();
      }, 30000, 'Plan generation'); // 30s timeout
    }, 'Plan generation request');
  }, 'Plan generation');
}

/**
 * 上传执行结果 - with retry and circuit breaker
 */
export async function uploadResult(
  planId: string,
  results: StepResult[],
  overallStatus: 'success' | 'failed',
  errorMessage?: string,
) {
  return uploadCircuitBreaker.execute(async () => {
    return retryHandler.executeWithRetry(async () => {
      return TimeoutHandler.withTimeout(async () => {
        const resp = await fetch(`${SERVER_URL}/result`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            planId,
            results,
            overallStatus,
            errorMessage,
          }),
        });
        
        if (!resp.ok) {
          const errorText = await resp.text();
          throw new Error(`Failed to upload result: ${resp.status} ${errorText}`);
        }
        
        return resp.json();
      }, 15000, 'Result upload'); // 15s timeout
    }, 'Result upload request');
  }, 'Result upload');
}

/**
 * 上传DOM树映射 - with retry and timeout
 */
export async function uploadDomTree(domTree: any): Promise<string> {
  return retryHandler.executeWithRetry(async () => {
    return TimeoutHandler.withTimeout(async () => {
      const resp = await fetch(`${SERVER_URL}/upload/dom`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domTree }),
      });
      
      if (!resp.ok) {
        const errorText = await resp.text();
        throw new Error(`Failed to upload dom tree: ${resp.status} ${errorText}`);
      }
      
      const data = await resp.json();
      return data.domId;
    }, 20000, 'DOM tree upload'); // 20s timeout
  }, 'DOM tree upload request');
}

/**
 * 生成扁平化DOM树映射
 * 返回 { rootId, map }
 */
export function buildDomTreeMap(): {
  rootId: string;
  map: Record<string, any>;
} {
  const map: Record<string, any> = {};
  let idCounter = 0;

  function traverse(node: Node): string | null {
    if (!node) return null;

    const id = `${idCounter++}`;

    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      if (!text) return null;
      map[id] = {
        type: 'TEXT_NODE',
        text,
        isVisible: true,
      };
      return id;
    }

    if (node.nodeType !== Node.ELEMENT_NODE) return null;

    const el = node as Element;
    const rect = (el as HTMLElement).getBoundingClientRect?.() || {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    };

    const childrenIds: string[] = [];
    for (const child of Array.from(el.childNodes)) {
      const childId = traverse(child);
      if (childId) childrenIds.push(childId);
    }

    map[id] = {
      tagName: el.tagName.toLowerCase(),
      attributes: Object.fromEntries(
        Array.from(el.attributes).map((attr) => [attr.name, attr.value]),
      ),
      xpath: getXPath(el),
      children: childrenIds,
      bbox: {
        x: rect.x || 0,
        y: rect.y || 0,
        width: rect.width || 0,
        height: rect.height || 0,
      },
      isVisible: true,
      isTopElement: true,
      isInteractive: false,
      highlightIndex: -1,
    };

    return id;
  }

  function getXPath(element: Element): string {
    if (element.id) return `//*[@id="${element.id}"]`;
    const parts = [];
    while (element && element.nodeType === Node.ELEMENT_NODE) {
      let index = 1;
      let sibling = element.previousElementSibling;
      while (sibling) {
        if (sibling.tagName === element.tagName) index++;
        sibling = sibling.previousElementSibling;
      }
      parts.unshift(`${element.tagName.toLowerCase()}[${index}]`);
      element = element.parentElement!;
    }
    return `/${parts.join('/')}`;
  }

  const rootId = traverse(document.body);
  return { rootId: rootId || '', map };
}
