/**
 * 百度搜索演示脚本
 * 演示如何使用Browser RPA系统执行"打开百度主页，搜索如何注册stripe，并获取前三个结果"
 */

import { chromium, Browser, Page } from 'playwright';

// 模拟DOM树构建函数
function buildDomTreeMap(page: Page) {
  return page.evaluate(() => {
    const map: Record<string, any> = {};
    let idCounter = 0;

    function traverse(node: any): string | null {
      if (!node) return null;
      const id = `${idCounter++}`;

      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (!text) return null;
        map[id] = {
          type: 'TEXT_NODE',
          text,
          isVisible: true,
        };
        return id;
      }

      if (node.nodeType !== Node.ELEMENT_NODE) return null;
      const el = node as Element;
      const rect = (el as HTMLElement).getBoundingClientRect?.() || {
        x: 0, y: 0, width: 0, height: 0,
      };

      const childrenIds: string[] = [];
      for (const child of Array.from(el.childNodes)) {
        const childId = traverse(child);
        if (childId) childrenIds.push(childId);
      }

      function getXPath(element: any): string {
        if (element.id) return `//*[@id="${element.id}"]`;
        const parts: string[] = [];
        while (element && element.nodeType === Node.ELEMENT_NODE) {
          let index = 1;
          let sibling = element.previousElementSibling;
          while (sibling) {
            if (sibling.tagName === element.tagName) index++;
            sibling = sibling.previousElementSibling;
          }
          parts.unshift(`${element.tagName.toLowerCase()}[${index}]`);
          element = element.parentElement;
        }
        return `/${parts.join('/')}`;
      }

      map[id] = {
        tagName: el.tagName.toLowerCase(),
        attributes: Object.fromEntries(
          Array.from(el.attributes).map((attr) => [attr.name, attr.value])
        ),
        xpath: getXPath(el),
        children: childrenIds,
        bbox: {
          x: rect.x || 0,
          y: rect.y || 0,
          width: rect.width || 0,
          height: rect.height || 0,
        },
        isVisible: true,
        isTopElement: true,
        isInteractive: false,
        highlightIndex: -1,
      };

      return id;
    }

    const rootId = traverse(document.body);
    return { rootId: rootId || '', map };
  });
}

// 模拟计划生成（简化版本）
function generateMockPlan(instruction: string, domTree: any) {
  console.log('🤖 正在分析DOM结构并生成执行计划...');
  
  const elements = Object.values(domTree.map);
  
  // 查找搜索相关元素
  const searchInput = elements.find((el: any) => 
    el.tagName === 'input' && 
    (el.attributes?.id === 'kw' || el.attributes?.name === 'wd')
  );
  
  const searchButton = elements.find((el: any) => 
    (el.tagName === 'input' && el.attributes?.id === 'su') ||
    (el.tagName === 'button' && el.attributes?.id === 'su')
  );

  const actions = [];

  if (searchInput) {
    actions.push({
      id: 'action-1',
      type: 'Locate',
      thought: '定位搜索输入框',
      locate: {
        prompt: '搜索输入框',
        bbox: searchInput.bbox
      }
    });

    actions.push({
      id: 'action-2',
      type: 'Input',
      thought: '在搜索框中输入"如何注册stripe"',
      param: '如何注册stripe',
      locate: {
        prompt: '搜索输入框',
        bbox: searchInput.bbox
      }
    });
  }

  if (searchButton) {
    actions.push({
      id: 'action-3',
      type: 'Tap',
      thought: '点击搜索按钮',
      locate: {
        prompt: '搜索按钮',
        bbox: searchButton.bbox
      }
    });
  }

  actions.push({
    id: 'action-4',
    type: 'Finished',
    thought: '等待搜索结果加载完成'
  });

  return {
    planId: `plan-${Date.now()}`,
    actions,
    instruction
  };
}

// 执行搜索操作
async function executeSearchPlan(page: Page, plan: any) {
  console.log('🚀 开始执行搜索计划...');
  
  const results = [];
  
  for (let i = 0; i < plan.actions.length; i++) {
    const action = plan.actions[i];
    console.log(`📍 执行步骤 ${i + 1}: ${action.type} - ${action.thought}`);
    
    try {
      switch (action.type) {
        case 'Locate':
          // 定位元素
          await page.waitForSelector('#kw', { timeout: 5000 });
          results.push({
            step: i + 1,
            id: action.id,
            status: 'success',
            log: '成功定位搜索输入框'
          });
          break;
          
        case 'Input':
          // 输入文本
          await page.fill('#kw', action.param);
          results.push({
            step: i + 1,
            id: action.id,
            status: 'success',
            log: `成功输入: ${action.param}`
          });
          break;
          
        case 'Tap':
          // 点击搜索按钮
          await page.click('#su');
          results.push({
            step: i + 1,
            id: action.id,
            status: 'success',
            log: '成功点击搜索按钮'
          });
          break;
          
        case 'Finished':
          // 等待搜索结果
          await page.waitForLoadState('networkidle', { timeout: 10000 });
          results.push({
            step: i + 1,
            id: action.id,
            status: 'success',
            log: '搜索完成，页面加载完毕'
          });
          break;
      }
      
      // 每步之间稍作停顿
      await page.waitForTimeout(1000);
      
    } catch (error) {
      console.log(`❌ 步骤 ${i + 1} 执行失败:`, error.message);
      results.push({
        step: i + 1,
        id: action.id,
        status: 'failed',
        log: `执行失败: ${error.message}`
      });
      break;
    }
  }
  
  return {
    results,
    overallStatus: results.every(r => r.status === 'success') ? 'success' : 'failed'
  };
}

// 获取搜索结果
async function getSearchResults(page: Page) {
  console.log('🔍 正在获取搜索结果...');
  
  try {
    // 等待搜索结果加载
    await page.waitForSelector('.result, .c-container', { timeout: 10000 });
    
    // 获取前三个搜索结果
    const results = await page.evaluate(() => {
      const resultSelectors = [
        '.result h3 a',
        '.c-container h3 a', 
        '.c-container .t a',
        '[data-log] h3 a'
      ];
      
      let links: Element[] = [];
      
      for (const selector of resultSelectors) {
        links = Array.from(document.querySelectorAll(selector));
        if (links.length > 0) break;
      }
      
      return links.slice(0, 3).map((link, index) => ({
        index: index + 1,
        title: link.textContent?.trim() || '',
        url: (link as HTMLAnchorElement).href || '',
        snippet: link.closest('.result, .c-container')?.querySelector('.c-abstract, .c-span9')?.textContent?.trim() || ''
      }));
    });
    
    return results;
  } catch (error) {
    console.log('⚠️ 获取搜索结果失败:', error.message);
    return [];
  }
}

// 主演示函数
async function demonstrateBaiduSearch() {
  console.log('🎬 开始演示百度搜索RPA流程');
  console.log('📋 任务: 打开百度主页，搜索"如何注册stripe"，并获取前三个结果');
  console.log('=' .repeat(60));
  
  let browser: Browser | null = null;
  
  try {
    // 1. 启动浏览器
    console.log('🌐 正在启动浏览器...');
    browser = await chromium.launch({ 
      headless: false, // 显示浏览器窗口以便观察
      slowMo: 1000 // 放慢操作速度以便观察
    });
    
    const context = await browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    });
    
    const page = await context.newPage();
    
    // 2. 打开百度主页
    console.log('🔗 正在打开百度主页...');
    await page.goto('https://www.baidu.com', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    console.log('✅ 百度主页加载完成');
    
    // 3. 提取DOM树
    console.log('🔍 正在分析页面结构...');
    const domTree = await buildDomTreeMap(page);
    console.log(`📊 页面分析完成，识别到 ${Object.keys(domTree.map).length} 个页面元素`);
    
    // 4. 生成执行计划
    const plan = generateMockPlan('打开百度主页，搜索"如何注册stripe"，并获取前三个结果', domTree);
    console.log(`📋 执行计划生成完成，包含 ${plan.actions.length} 个步骤:`);
    plan.actions.forEach((action, index) => {
      console.log(`   ${index + 1}. ${action.type}: ${action.thought}`);
    });
    
    // 5. 执行搜索计划
    const executionResult = await executeSearchPlan(page, plan);
    console.log(`📊 计划执行完成，状态: ${executionResult.overallStatus}`);
    
    // 6. 获取搜索结果
    if (executionResult.overallStatus === 'success') {
      const searchResults = await getSearchResults(page);
      
      if (searchResults.length > 0) {
        console.log('🎯 成功获取搜索结果:');
        console.log('=' .repeat(60));
        searchResults.forEach(result => {
          console.log(`${result.index}. ${result.title}`);
          console.log(`   URL: ${result.url}`);
          if (result.snippet) {
            console.log(`   摘要: ${result.snippet.substring(0, 100)}...`);
          }
          console.log('');
        });
      } else {
        console.log('⚠️ 未能获取到搜索结果，可能是页面结构变化');
      }
      
      // 截图保存结果
      await page.screenshot({ 
        path: 'baidu-search-demo-result.png',
        fullPage: true 
      });
      console.log('📸 搜索结果截图已保存为 baidu-search-demo-result.png');
      
    } else {
      console.log('❌ 搜索执行失败');
      executionResult.results.forEach(result => {
        console.log(`   步骤 ${result.step}: ${result.status} - ${result.log}`);
      });
    }
    
    // 7. 等待用户观察结果
    console.log('⏳ 等待 5 秒以便观察结果...');
    await page.waitForTimeout(5000);
    
    console.log('🎉 百度搜索RPA演示完成！');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 运行演示
if (require.main === module) {
  demonstrateBaiduSearch().catch(console.error);
}

export { demonstrateBaiduSearch };
