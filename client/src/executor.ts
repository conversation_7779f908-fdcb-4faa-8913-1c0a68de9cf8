import type { GeneratePlanResponse, StepResult } from './api';

interface ExecutionContext {
  page?: any; // Playwright page object
  timeout?: number;
}

/**
 * Real browser automation executor
 * Executes plans using actual DOM interactions
 */
export async function executePlan(
  plan: GeneratePlanResponse,
  context: ExecutionContext = {}
): Promise<{
  results: StepResult[];
  overallStatus: 'success' | 'failed';
  errorMessage?: string;
}> {
  const results: StepResult[] = [];
  let overallStatus: 'success' | 'failed' = 'success';
  let errorMessage = '';
  const { page, timeout = 10000 } = context;

  // If no page context provided, fall back to document-based execution
  const isPlaywrightMode = !!page;

  for (let i = 0; i < plan.actions.length; i++) {
    const action = plan.actions[i];
    let stepResult: StepResult;

    try {
      console.log(`执行第${i + 1}步: ${action.type}`);
      
      stepResult = await executeAction(action, isPlaywrightMode ? page : null, timeout);
      stepResult.step = i + 1;
      stepResult.id = action.id;
      
      results.push(stepResult);
      
      if (stepResult.status === 'failed') {
        overallStatus = 'failed';
        errorMessage = stepResult.log || '执行失败';
        break;
      }
    } catch (err: any) {
      overallStatus = 'failed';
      errorMessage = err.message || '执行失败';
      results.push({
        step: i + 1,
        id: action.id,
        status: 'failed',
        screenshot: await captureScreenshot(page),
        log: errorMessage,
      });
      break;
    }
  }

  return { results, overallStatus, errorMessage };
}

/**
 * Execute individual action
 */
async function executeAction(
  action: any,
  page: any | null,
  timeout: number
): Promise<StepResult> {
  const screenshot = await captureScreenshot(page);
  
  switch (action.type) {
    case 'Locate':
      return await executeLocate(action, page, timeout, screenshot);
    case 'Tap':
      return await executeTap(action, page, timeout, screenshot);
    case 'Input':
      return await executeInput(action, page, timeout, screenshot);
    case 'Scroll':
      return await executeScroll(action, page, timeout, screenshot);
    case 'KeyboardPress':
      return await executeKeyboardPress(action, page, timeout, screenshot);
    case 'Drag':
      return await executeDrag(action, page, timeout, screenshot);
    case 'Finished':
      return {
        step: 0,
        status: 'success',
        screenshot,
        log: '执行完成',
      };
    default:
      return {
        step: 0,
        status: 'failed',
        screenshot,
        log: `未知操作类型: ${action.type}`,
      };
  }
}

/**
 * Locate element using bbox or XPath
 */
async function executeLocate(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  const { locate } = action;
  if (!locate) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: '缺少定位信息',
    };
  }

  try {
    if (page) {
      // Playwright mode - use coordinates or selectors
      if (locate.bbox) {
        const { x, y, width, height } = locate.bbox;
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        
        // Check if element exists at coordinates
        const element = await page.elementHandle(`xpath=//body`);
        if (element) {
          const box = await element.boundingBox();
          if (box && centerX >= box.x && centerX <= box.x + box.width &&
              centerY >= box.y && centerY <= box.y + box.height) {
            return {
              step: 0,
              status: 'success',
              screenshot,
              log: `成功定位元素: ${locate.prompt}`,
            };
          }
        }
      }
    } else {
      // Document mode - use DOM queries
      const element = findElementByBbox(locate.bbox) || findElementByXPath(locate.xpath);
      if (element) {
        return {
          step: 0,
          status: 'success',
          screenshot,
          log: `成功定位元素: ${locate.prompt}`,
        };
      }
    }
    
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `无法定位元素: ${locate.prompt}`,
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `定位失败: ${err.message}`,
    };
  }
}

/**
 * Click element
 */
async function executeTap(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  const { locate } = action;
  if (!locate) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: '缺少点击目标定位信息',
    };
  }

  try {
    if (page) {
      // Playwright mode
      if (locate.bbox) {
        const { x, y, width, height } = locate.bbox;
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        await page.click(`xpath=//body`, { 
          position: { x: centerX, y: centerY },
          timeout 
        });
      } else {
        return {
          step: 0,
          status: 'failed',
          screenshot,
          log: '缺少坐标信息',
        };
      }
    } else {
      // Document mode
      const element = findElementByBbox(locate.bbox) || findElementByXPath(locate.xpath);
      if (element && element instanceof HTMLElement) {
        element.click();
      } else {
        return {
          step: 0,
          status: 'failed',
          screenshot,
          log: '无法找到点击目标',
        };
      }
    }
    
    return {
      step: 0,
      status: 'success',
      screenshot: await captureScreenshot(page),
      log: `成功点击: ${locate.prompt}`,
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `点击失败: ${err.message}`,
    };
  }
}

/**
 * Input text
 */
async function executeInput(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  const { locate, param } = action;
  if (!locate || !param) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: '缺少输入目标或内容',
    };
  }

  try {
    if (page) {
      // Playwright mode
      if (locate.bbox) {
        const { x, y, width, height } = locate.bbox;
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        await page.click(`xpath=//body`, { 
          position: { x: centerX, y: centerY },
          timeout 
        });
        await page.keyboard.type(param);
      }
    } else {
      // Document mode
      const element = findElementByBbox(locate.bbox) || findElementByXPath(locate.xpath);
      if (element && (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement)) {
        element.focus();
        element.value = param;
        element.dispatchEvent(new Event('input', { bubbles: true }));
      } else {
        return {
          step: 0,
          status: 'failed',
          screenshot,
          log: '无法找到输入框',
        };
      }
    }
    
    return {
      step: 0,
      status: 'success',
      screenshot: await captureScreenshot(page),
      log: `成功输入: ${param}`,
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `输入失败: ${err.message}`,
    };
  }
}

/**
 * Scroll page
 */
async function executeScroll(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  try {
    const { param } = action;
    const scrollAmount = param?.amount || 500;
    const direction = param?.direction || 'down';
    
    if (page) {
      // Playwright mode
      if (direction === 'down') {
        await page.mouse.wheel(0, scrollAmount);
      } else if (direction === 'up') {
        await page.mouse.wheel(0, -scrollAmount);
      }
    } else {
      // Document mode
      if (direction === 'down') {
        window.scrollBy(0, scrollAmount);
      } else if (direction === 'up') {
        window.scrollBy(0, -scrollAmount);
      }
    }
    
    return {
      step: 0,
      status: 'success',
      screenshot: await captureScreenshot(page),
      log: `成功滚动: ${direction} ${scrollAmount}px`,
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `滚动失败: ${err.message}`,
    };
  }
}

/**
 * Press keyboard keys
 */
async function executeKeyboardPress(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  try {
    const { param } = action;
    const key = param?.key || 'Enter';
    
    if (page) {
      // Playwright mode
      await page.keyboard.press(key);
    } else {
      // Document mode
      const keyboardEvent = new KeyboardEvent('keydown', {
        key: key,
        bubbles: true
      });
      document.dispatchEvent(keyboardEvent);
    }
    
    return {
      step: 0,
      status: 'success',
      screenshot: await captureScreenshot(page),
      log: `成功按键: ${key}`,
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `按键失败: ${err.message}`,
    };
  }
}

/**
 * Drag element
 */
async function executeDrag(
  action: any,
  page: any | null,
  timeout: number,
  screenshot: string
): Promise<StepResult> {
  try {
    const { locate, param } = action;
    if (!locate || !param) {
      return {
        step: 0,
        status: 'failed',
        screenshot,
        log: '缺少拖拽信息',
      };
    }

    if (page) {
      // Playwright mode
      const { x, y, width, height } = locate.bbox;
      const startX = x + width / 2;
      const startY = y + height / 2;
      const endX = param.x || startX + 100;
      const endY = param.y || startY + 100;
      
      await page.mouse.move(startX, startY);
      await page.mouse.down();
      await page.mouse.move(endX, endY);
      await page.mouse.up();
    } else {
      return {
        step: 0,
        status: 'failed',
        screenshot,
        log: '拖拽操作需要 Playwright 环境',
      };
    }
    
    return {
      step: 0,
      status: 'success',
      screenshot: await captureScreenshot(page),
      log: '成功执行拖拽',
    };
  } catch (err: any) {
    return {
      step: 0,
      status: 'failed',
      screenshot,
      log: `拖拽失败: ${err.message}`,
    };
  }
}

/**
 * Capture screenshot
 */
async function captureScreenshot(page: any | null): Promise<string> {
  if (page) {
    try {
      const screenshot = await page.screenshot({ encoding: 'base64' });
      return screenshot;
    } catch (err) {
      console.error('Screenshot capture failed:', err);
      return '';
    }
  }
  return ''; // No screenshot in document mode
}

/**
 * Find element by bounding box coordinates
 */
function findElementByBbox(bbox: any): Element | null {
  if (!bbox) return null;
  
  const { x, y, width, height } = bbox;
  const centerX = x + width / 2;
  const centerY = y + height / 2;
  
  return document.elementFromPoint(centerX, centerY);
}

/**
 * Find element by XPath
 */
function findElementByXPath(xpath: string): Element | null {
  if (!xpath) return null;
  
  try {
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    return result.singleNodeValue as Element;
  } catch (err) {
    console.error('XPath evaluation failed:', err);
    return null;
  }
}
