import {
  type ConversationMessage,
  buildDomTreeMap,
  requestPlan,
  uploadDomTree,
  uploadResult,
} from './api';
import { executePlan } from './executor';

async function main() {
  const userInstruction = '点击登录按钮';
  const conversationHistory: ConversationMessage[] = [
    { role: 'user', content: '打开首页' },
    { role: 'assistant', content: '已打开首页' },
  ];
  const size = { width: 1920, height: 1080 };

  // 模拟一个测试页面HTML
  const html = `
    <html>
      <body>
        <div id="container">
          <button id="loginBtn" class="btn">登录</button>
        </div>
      </body>
    </html>
  `;

  // 将测试HTML插入当前页面
  document.body.innerHTML = html;

  // 在当前页面构建DOM树映射
  const { rootId, map } = buildDomTreeMap();

  console.log('生成的DOM树映射:', { rootId, map });

  // 上传DOM树
  const domId = await uploadDomTree({ rootId, map });
  console.log('上传DOM树成功，domId:', domId);

  console.log('请求生成Plan...');
  const plan = await requestPlan(
    userInstruction,
    conversationHistory,
    size,
    undefined,
    domId,
  );
  console.log('收到Plan:', JSON.stringify(plan, null, 2));

  console.log('开始执行Plan...');
  const { results, overallStatus, errorMessage } = await executePlan(plan);

  console.log('上传执行结果...');
  const resp = await uploadResult(
    plan.planId,
    results,
    overallStatus,
    errorMessage,
  );
  console.log('上传结果响应:', resp);
}

main().catch((err) => {
  console.error('客户端运行出错:', err);
});
