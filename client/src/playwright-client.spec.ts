import { expect, test } from '@playwright/test';
import { buildDomTreeMap, requestPlan, uploadDomTree } from './api';
import { executePlan } from './executor';

test('示例测试：页面标题应包含 Playwright', async ({ page }) => {
  await page.goto('https://playwright.dev/');
  await expect(page).toHaveTitle(/Playwright/);
});

test('端到端：DOM树提取、上传、plan获取与执行', async ({ page }) => {
  // 1. 打开待测页面
  await page.goto('https://todomvc.com/examples/react/dist/');

  // 2. 执行页面操作，确保有内容
  await page.click('input.new-todo');
  await page.fill('input.new-todo', '使用 Playwright 测试');
  await page.keyboard.press('Enter');

  // 3. 提取 DOM 树结构（在页面上下文执行 buildDomTreeMap）
  const domTree = await page.evaluate(() => {
    // 直接实现 buildDomTreeMap，保证在页面上下文可用
    function buildDomTreeMap() {
      const map: Record<string, any> = {};
      let idCounter = 0;
      function traverse(node: any): string | null {
        if (!node) return null;
        const id = `${idCounter++}`;
        if (node.nodeType === Node.TEXT_NODE) {
          const text = node.textContent?.trim();
          if (!text) return null;
          map[id] = {
            type: 'TEXT_NODE',
            text,
            isVisible: true,
          };
          return id;
        }
        if (node.nodeType !== Node.ELEMENT_NODE) return null;
        const el = node as Element;
        const rect = (el as HTMLElement).getBoundingClientRect?.() || {
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        };
        const childrenIds: string[] = [];
        for (const child of Array.from(el.childNodes)) {
          const childId = traverse(child);
          if (childId) childrenIds.push(childId);
        }
        function getXPath(element: any): string {
          if (element.id) return `//*[@id="${element.id}"]`;
          const parts: string[] = [];
          while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 1;
            let sibling = element.previousElementSibling;
            while (sibling) {
              if (sibling.tagName === element.tagName) index++;
              sibling = sibling.previousElementSibling;
            }
            parts.unshift(`${element.tagName.toLowerCase()}[${index}]`);
            element = element.parentElement;
          }
          return `/${parts.join('/')}`;
        }
        map[id] = {
          tagName: el.tagName.toLowerCase(),
          attributes: Object.fromEntries(
            Array.from(el.attributes).map((attr) => {
              const a = attr as Attr;
              return [a.name, a.value];
            }),
          ),
          xpath: getXPath(el),
          children: childrenIds,
          bbox: {
            x: rect.x || 0,
            y: rect.y || 0,
            width: rect.width || 0,
            height: rect.height || 0,
          },
          isVisible: true,
          isTopElement: true,
          isInteractive: false,
          highlightIndex: -1,
        };
        return id;
      }
      const rootId = traverse(document.body);
      return { rootId: rootId || '', map };
    }
    return buildDomTreeMap();
  });

  expect(domTree).toBeTruthy();
  // 4. 上传 DOM 树，获取 domId
  let domId = '';
  if (domTree) {
    domId = await uploadDomTree(domTree);
    expect(domId).toBeTruthy();
  }

  // 5. 请求 plan（此处为示例，需保证本地 server 可用）
  const plan = await requestPlan(
    '请完成一个添加待办事项的操作',
    [],
    { width: 1024, height: 768 },
    domId,
  );
  expect(plan).toBeTruthy();
  expect(plan.actions.length).toBeGreaterThan(0);
  console.log('收到Plan:', JSON.stringify(plan, null, 2));

  // 6. 执行 plan
  const execResult = await executePlan(plan);
  expect(execResult.overallStatus).toBe('success');
  // 可根据需要断言 execResult.results 的内容
});
