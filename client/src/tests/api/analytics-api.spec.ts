import { test, expect } from '@playwright/test';
import { verifyServerHealth } from '../utils/test-helpers';

test.describe('Analytics API Integration Tests', () => {
  const baseURL = 'http://localhost:3000/api/analytics';

  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should get dashboard statistics', async ({ request }) => {
    const response = await request.get(`${baseURL}/dashboard`);
    
    expect(response.ok()).toBeTruthy();
    
    const dashboard = await response.json();
    expect(dashboard).toBeTruthy();
    
    // Verify dashboard structure
    if (dashboard.totalPlans !== undefined) {
      expect(typeof dashboard.totalPlans).toBe('number');
    }
    if (dashboard.totalExecutions !== undefined) {
      expect(typeof dashboard.totalExecutions).toBe('number');
    }
    if (dashboard.successRate !== undefined) {
      expect(typeof dashboard.successRate).toBe('number');
      expect(dashboard.successRate).toBeGreaterThanOrEqual(0);
      expect(dashboard.successRate).toBeLessThanOrEqual(100);
    }
  });

  test('should get execution statistics with different time ranges', async ({ request }) => {
    const timeRanges = ['1d', '7d', '30d'];
    
    for (const timeRange of timeRanges) {
      const response = await request.get(`${baseURL}/executions/stats?timeRange=${timeRange}&groupBy=day`);
      
      expect(response.ok()).toBeTruthy();
      
      const stats = await response.json();
      expect(stats).toBeTruthy();
      expect(Array.isArray(stats) || typeof stats === 'object').toBeTruthy();
    }
  });

  test('should get plan performance metrics', async ({ request }) => {
    const response = await request.get(`${baseURL}/plans/performance?limit=10`);
    
    expect(response.ok()).toBeTruthy();
    
    const performance = await response.json();
    expect(performance).toBeTruthy();
    
    if (Array.isArray(performance)) {
      expect(performance.length).toBeLessThanOrEqual(10);
      
      performance.forEach((plan: any) => {
        if (plan.planId) {
          expect(typeof plan.planId).toBe('string');
        }
        if (plan.executionTime !== undefined) {
          expect(typeof plan.executionTime).toBe('number');
          expect(plan.executionTime).toBeGreaterThanOrEqual(0);
        }
      });
    }
  });

  test('should get action frequency analysis', async ({ request }) => {
    const response = await request.get(`${baseURL}/actions/frequency?timeRange=7d`);
    
    expect(response.ok()).toBeTruthy();
    
    const frequency = await response.json();
    expect(frequency).toBeTruthy();
    
    if (Array.isArray(frequency)) {
      frequency.forEach((action: any) => {
        if (action.actionType) {
          expect(typeof action.actionType).toBe('string');
        }
        if (action.count !== undefined) {
          expect(typeof action.count).toBe('number');
          expect(action.count).toBeGreaterThanOrEqual(0);
        }
      });
    }
  });

  test('should get error analysis', async ({ request }) => {
    const response = await request.get(`${baseURL}/errors/analysis?timeRange=7d&limit=20`);
    
    expect(response.ok()).toBeTruthy();
    
    const errors = await response.json();
    expect(errors).toBeTruthy();
    
    if (Array.isArray(errors)) {
      expect(errors.length).toBeLessThanOrEqual(20);
      
      errors.forEach((error: any) => {
        if (error.errorType) {
          expect(typeof error.errorType).toBe('string');
        }
        if (error.count !== undefined) {
          expect(typeof error.count).toBe('number');
          expect(error.count).toBeGreaterThanOrEqual(0);
        }
      });
    }
  });

  test('should get success rate trends', async ({ request }) => {
    const response = await request.get(`${baseURL}/trends/success-rate?timeRange=7d&groupBy=day`);
    
    expect(response.ok()).toBeTruthy();
    
    const trends = await response.json();
    expect(trends).toBeTruthy();
    
    if (Array.isArray(trends)) {
      trends.forEach((trend: any) => {
        if (trend.date) {
          expect(typeof trend.date).toBe('string');
        }
        if (trend.successRate !== undefined) {
          expect(typeof trend.successRate).toBe('number');
          expect(trend.successRate).toBeGreaterThanOrEqual(0);
          expect(trend.successRate).toBeLessThanOrEqual(100);
        }
      });
    }
  });

  test('should handle invalid time range parameters', async ({ request }) => {
    const response = await request.get(`${baseURL}/executions/stats?timeRange=invalid&groupBy=day`);
    
    // Should either return default data or appropriate error
    if (response.ok()) {
      const stats = await response.json();
      expect(stats).toBeTruthy();
    } else {
      expect(response.status()).toBeGreaterThanOrEqual(400);
    }
  });

  test('should handle invalid limit parameters', async ({ request }) => {
    const response = await request.get(`${baseURL}/plans/performance?limit=-1`);
    
    // Should either return default data or appropriate error
    if (response.ok()) {
      const performance = await response.json();
      expect(performance).toBeTruthy();
    } else {
      expect(response.status()).toBeGreaterThanOrEqual(400);
    }
  });

  test('should handle large limit parameters', async ({ request }) => {
    const response = await request.get(`${baseURL}/plans/performance?limit=10000`);
    
    expect(response.ok()).toBeTruthy();
    
    const performance = await response.json();
    expect(performance).toBeTruthy();
    
    // Should handle large limits gracefully (either cap the results or return all available)
    if (Array.isArray(performance)) {
      expect(performance.length).toBeGreaterThanOrEqual(0);
    }
  });

  test('should return consistent data structure across endpoints', async ({ request }) => {
    const endpoints = [
      '/dashboard',
      '/executions/stats?timeRange=7d',
      '/plans/performance?limit=5',
      '/actions/frequency?timeRange=7d',
      '/errors/analysis?timeRange=7d',
      '/trends/success-rate?timeRange=7d'
    ];

    for (const endpoint of endpoints) {
      const response = await request.get(`${baseURL}${endpoint}`);
      
      expect(response.ok()).toBeTruthy();
      
      const data = await response.json();
      expect(data).toBeTruthy();
      
      // Verify response is valid JSON
      expect(typeof data).toBe('object');
    }
  });

  test('should handle concurrent analytics requests', async ({ request }) => {
    const promises = [
      request.get(`${baseURL}/dashboard`),
      request.get(`${baseURL}/executions/stats?timeRange=7d`),
      request.get(`${baseURL}/plans/performance?limit=10`),
      request.get(`${baseURL}/actions/frequency?timeRange=7d`),
      request.get(`${baseURL}/errors/analysis?timeRange=7d`)
    ];

    const responses = await Promise.all(promises);

    // All requests should succeed
    responses.forEach(response => {
      expect(response.ok()).toBeTruthy();
    });

    // Parse all responses
    const results = await Promise.all(responses.map(r => r.json()));
    
    // All results should be valid
    results.forEach(result => {
      expect(result).toBeTruthy();
      expect(typeof result).toBe('object');
    });
  });

  test('should handle different groupBy parameters', async ({ request }) => {
    const groupByOptions = ['hour', 'day', 'week', 'month'];
    
    for (const groupBy of groupByOptions) {
      const response = await request.get(`${baseURL}/executions/stats?timeRange=7d&groupBy=${groupBy}`);
      
      // Should either succeed or handle invalid groupBy gracefully
      if (response.ok()) {
        const stats = await response.json();
        expect(stats).toBeTruthy();
      } else {
        expect(response.status()).toBeGreaterThanOrEqual(400);
      }
    }
  });

  test('should validate response times for analytics queries', async ({ request }) => {
    const startTime = Date.now();
    
    const response = await request.get(`${baseURL}/dashboard`);
    
    const responseTime = Date.now() - startTime;
    
    expect(response.ok()).toBeTruthy();
    
    // Analytics queries should be reasonably fast (less than 10 seconds)
    expect(responseTime).toBeLessThan(10000);
    
    const dashboard = await response.json();
    expect(dashboard).toBeTruthy();
  });

  test('should handle empty data scenarios', async ({ request }) => {
    // Test analytics endpoints when there might be no data
    const response = await request.get(`${baseURL}/plans/performance?limit=100`);
    
    expect(response.ok()).toBeTruthy();
    
    const performance = await response.json();
    expect(performance).toBeTruthy();
    
    // Should handle empty results gracefully
    if (Array.isArray(performance)) {
      expect(performance.length).toBeGreaterThanOrEqual(0);
    } else {
      expect(typeof performance).toBe('object');
    }
  });
});
