import { test, expect } from '@playwright/test';
import { verifyServerHealth } from '../utils/test-helpers';

test.describe('Cleanup API Integration Tests', () => {
  const baseURL = 'http://localhost:3000/api/cleanup';

  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should get cleanup status', async ({ request }) => {
    const response = await request.get(`${baseURL}/status`);
    
    expect(response.ok()).toBeTruthy();
    
    const status = await response.json();
    expect(status).toBeTruthy();
    expect(status.success).toBe(true);
    
    // Verify status structure
    if (status.config) {
      expect(typeof status.config).toBe('object');
      
      if (status.config.retentionPeriodDays !== undefined) {
        expect(typeof status.config.retentionPeriodDays).toBe('number');
        expect(status.config.retentionPeriodDays).toBeGreaterThan(0);
      }
      
      if (status.config.maxRecordsPerTable !== undefined) {
        expect(typeof status.config.maxRecordsPerTable).toBe('number');
        expect(status.config.maxRecordsPerTable).toBeGreaterThan(0);
      }
      
      if (status.config.cleanupScreenshots !== undefined) {
        expect(typeof status.config.cleanupScreenshots).toBe('boolean');
      }
    }
    
    if (status.scheduledCleanupEnabled !== undefined) {
      expect(typeof status.scheduledCleanupEnabled).toBe('boolean');
    }
  });

  test('should get cleanup preview', async ({ request }) => {
    const response = await request.get(`${baseURL}/preview`);
    
    expect(response.ok()).toBeTruthy();
    
    const preview = await response.json();
    expect(preview).toBeTruthy();
    expect(preview.success).toBe(true);
    
    // Verify preview structure
    if (preview.preview) {
      expect(typeof preview.preview).toBe('object');
      
      // Check for expected preview fields
      const previewData = preview.preview;
      
      if (previewData.plansToDelete !== undefined) {
        expect(typeof previewData.plansToDelete).toBe('number');
        expect(previewData.plansToDelete).toBeGreaterThanOrEqual(0);
      }
      
      if (previewData.executionsToDelete !== undefined) {
        expect(typeof previewData.executionsToDelete).toBe('number');
        expect(previewData.executionsToDelete).toBeGreaterThanOrEqual(0);
      }
      
      if (previewData.screenshotsToDelete !== undefined) {
        expect(typeof previewData.screenshotsToDelete).toBe('number');
        expect(previewData.screenshotsToDelete).toBeGreaterThanOrEqual(0);
      }
      
      if (previewData.estimatedBytesToFree !== undefined) {
        expect(typeof previewData.estimatedBytesToFree).toBe('number');
        expect(previewData.estimatedBytesToFree).toBeGreaterThanOrEqual(0);
      }
    }
    
    if (preview.config) {
      expect(typeof preview.config).toBe('object');
    }
  });

  test('should run manual cleanup', async ({ request }) => {
    const response = await request.post(`${baseURL}/run`);
    
    expect(response.ok()).toBeTruthy();
    
    const result = await response.json();
    expect(result).toBeTruthy();
    expect(result.success).toBe(true);
    
    // Verify cleanup result structure
    if (result.result) {
      expect(typeof result.result).toBe('object');
      
      const cleanupResult = result.result;
      
      if (cleanupResult.plansDeleted !== undefined) {
        expect(typeof cleanupResult.plansDeleted).toBe('number');
        expect(cleanupResult.plansDeleted).toBeGreaterThanOrEqual(0);
      }
      
      if (cleanupResult.executionsDeleted !== undefined) {
        expect(typeof cleanupResult.executionsDeleted).toBe('number');
        expect(cleanupResult.executionsDeleted).toBeGreaterThanOrEqual(0);
      }
      
      if (cleanupResult.actionsDeleted !== undefined) {
        expect(typeof cleanupResult.actionsDeleted).toBe('number');
        expect(cleanupResult.actionsDeleted).toBeGreaterThanOrEqual(0);
      }
      
      if (cleanupResult.screenshotsDeleted !== undefined) {
        expect(typeof cleanupResult.screenshotsDeleted).toBe('number');
        expect(cleanupResult.screenshotsDeleted).toBeGreaterThanOrEqual(0);
      }
      
      if (cleanupResult.bytesFreed !== undefined) {
        expect(typeof cleanupResult.bytesFreed).toBe('number');
        expect(cleanupResult.bytesFreed).toBeGreaterThanOrEqual(0);
      }
      
      if (cleanupResult.duration !== undefined) {
        expect(typeof cleanupResult.duration).toBe('number');
        expect(cleanupResult.duration).toBeGreaterThanOrEqual(0);
      }
    }
  });

  test('should handle multiple concurrent cleanup requests', async ({ request }) => {
    // First request
    const response1Promise = request.post(`${baseURL}/run`);
    
    // Second request (should handle concurrent access gracefully)
    const response2Promise = request.post(`${baseURL}/run`);
    
    const [response1, response2] = await Promise.all([response1Promise, response2Promise]);
    
    // At least one should succeed
    const success1 = response1.ok();
    const success2 = response2.ok();
    
    expect(success1 || success2).toBeTruthy();
    
    if (success1) {
      const result1 = await response1.json();
      expect(result1.success).toBe(true);
    }
    
    if (success2) {
      const result2 = await response2.json();
      expect(result2.success).toBe(true);
    }
  });

  test('should validate cleanup timing', async ({ request }) => {
    const startTime = Date.now();
    
    const response = await request.post(`${baseURL}/run`);
    
    const cleanupTime = Date.now() - startTime;
    
    expect(response.ok()).toBeTruthy();
    
    const result = await response.json();
    expect(result.success).toBe(true);
    
    // Cleanup should complete in reasonable time (less than 60 seconds)
    expect(cleanupTime).toBeLessThan(60000);
    
    // If duration is reported, it should be consistent with measured time
    if (result.result && result.result.duration !== undefined) {
      const reportedDuration = result.result.duration;
      expect(reportedDuration).toBeGreaterThan(0);
      expect(reportedDuration).toBeLessThanOrEqual(cleanupTime + 1000); // Allow 1s tolerance
    }
  });

  test('should handle cleanup with custom parameters', async ({ request }) => {
    const customCleanupRequest = {
      retentionPeriodDays: 7,
      maxRecordsPerTable: 1000,
      cleanupScreenshots: true,
      testMode: true
    };

    const response = await request.post(`${baseURL}/run`, {
      data: customCleanupRequest
    });
    
    // Should either accept custom parameters or use defaults
    if (response.ok()) {
      const result = await response.json();
      expect(result.success).toBe(true);
    } else {
      // If custom parameters aren't supported, should return appropriate error
      expect(response.status()).toBeGreaterThanOrEqual(400);
    }
  });

  test('should provide consistent preview and actual cleanup results', async ({ request }) => {
    // Get preview first
    const previewResponse = await request.get(`${baseURL}/preview`);
    expect(previewResponse.ok()).toBeTruthy();
    
    const preview = await previewResponse.json();
    expect(preview.success).toBe(true);
    
    // Run actual cleanup
    const cleanupResponse = await request.post(`${baseURL}/run`);
    expect(cleanupResponse.ok()).toBeTruthy();
    
    const cleanup = await cleanupResponse.json();
    expect(cleanup.success).toBe(true);
    
    // Compare preview vs actual results (allowing for some variance due to timing)
    if (preview.preview && cleanup.result) {
      const previewData = preview.preview;
      const cleanupData = cleanup.result;
      
      if (previewData.plansToDelete !== undefined && cleanupData.plansDeleted !== undefined) {
        // Actual deleted should be <= preview (some records might be added between preview and cleanup)
        expect(cleanupData.plansDeleted).toBeLessThanOrEqual(previewData.plansToDelete + 10);
      }
      
      if (previewData.executionsToDelete !== undefined && cleanupData.executionsDeleted !== undefined) {
        expect(cleanupData.executionsDeleted).toBeLessThanOrEqual(previewData.executionsToDelete + 10);
      }
    }
  });

  test('should handle cleanup status during and after cleanup', async ({ request }) => {
    // Start cleanup
    const cleanupPromise = request.post(`${baseURL}/run`);
    
    // Check status during cleanup (might be running)
    const statusResponse = await request.get(`${baseURL}/status`);
    expect(statusResponse.ok()).toBeTruthy();
    
    const status = await statusResponse.json();
    expect(status.success).toBe(true);
    
    // Wait for cleanup to complete
    const cleanupResponse = await cleanupPromise;
    expect(cleanupResponse.ok()).toBeTruthy();
    
    // Check status after cleanup
    const finalStatusResponse = await request.get(`${baseURL}/status`);
    expect(finalStatusResponse.ok()).toBeTruthy();
    
    const finalStatus = await finalStatusResponse.json();
    expect(finalStatus.success).toBe(true);
  });

  test('should handle invalid cleanup requests', async ({ request }) => {
    const invalidRequest = {
      invalidParameter: 'invalid-value',
      retentionPeriodDays: -1, // Invalid negative value
      maxRecordsPerTable: 'not-a-number' // Invalid type
    };

    const response = await request.post(`${baseURL}/run`, {
      data: invalidRequest
    });
    
    // Should either ignore invalid parameters and use defaults, or return error
    if (!response.ok()) {
      expect(response.status()).toBeGreaterThanOrEqual(400);
    } else {
      const result = await response.json();
      expect(result).toBeTruthy();
    }
  });

  test('should validate cleanup configuration consistency', async ({ request }) => {
    const statusResponse = await request.get(`${baseURL}/status`);
    expect(statusResponse.ok()).toBeTruthy();
    
    const status = await statusResponse.json();
    
    const previewResponse = await request.get(`${baseURL}/preview`);
    expect(previewResponse.ok()).toBeTruthy();
    
    const preview = await previewResponse.json();
    
    // Configuration should be consistent between status and preview
    if (status.config && preview.config) {
      expect(status.config.retentionPeriodDays).toBe(preview.config.retentionPeriodDays);
      expect(status.config.maxRecordsPerTable).toBe(preview.config.maxRecordsPerTable);
      expect(status.config.cleanupScreenshots).toBe(preview.config.cleanupScreenshots);
    }
  });
});
