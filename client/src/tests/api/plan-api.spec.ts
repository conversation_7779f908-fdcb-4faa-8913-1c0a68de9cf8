import { test, expect } from '@playwright/test';
import { verifyServerHealth } from '../utils/test-helpers';

test.describe('Plan API Integration Tests', () => {
  const baseURL = 'http://localhost:3000/api/plan';

  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should check server health', async ({ request }) => {
    const response = await request.get(`${baseURL}/health`);
    
    expect(response.ok()).toBeTruthy();
    
    const health = await response.json();
    expect(health.status).toBe('healthy');
    expect(health.timestamp).toBeTruthy();
  });

  test('should upload DOM tree successfully', async ({ request }) => {
    const domTree = {
      rootId: '0',
      map: {
        '0': {
          tagName: 'div',
          attributes: { id: 'test-container' },
          xpath: '//*[@id="test-container"]',
          children: ['1'],
          bbox: { x: 0, y: 0, width: 100, height: 50 },
          isVisible: true,
          isTopElement: true,
          isInteractive: false,
          highlightIndex: -1,
        },
        '1': {
          tagName: 'button',
          attributes: { id: 'test-btn', class: 'btn' },
          xpath: '//*[@id="test-btn"]',
          children: [],
          bbox: { x: 10, y: 10, width: 80, height: 30 },
          isVisible: true,
          isTopElement: true,
          isInteractive: true,
          highlightIndex: -1,
        }
      }
    };

    const response = await request.post(`${baseURL}/upload/dom`, {
      data: { domTree }
    });

    expect(response.ok()).toBeTruthy();
    
    const result = await response.json();
    expect(result.domId).toBeTruthy();
    expect(result.success).toBe(true);
    expect(result.size).toBeGreaterThan(0);
  });

  test('should generate plan successfully', async ({ request }) => {
    // First upload a DOM tree
    const domTree = {
      rootId: '0',
      map: {
        '0': {
          tagName: 'div',
          attributes: { id: 'login-form' },
          xpath: '//*[@id="login-form"]',
          children: ['1', '2', '3'],
          bbox: { x: 0, y: 0, width: 300, height: 200 },
          isVisible: true,
          isTopElement: true,
          isInteractive: false,
          highlightIndex: -1,
        },
        '1': {
          tagName: 'input',
          attributes: { id: 'username', type: 'text', placeholder: 'Username' },
          xpath: '//*[@id="username"]',
          children: [],
          bbox: { x: 10, y: 10, width: 280, height: 40 },
          isVisible: true,
          isTopElement: true,
          isInteractive: true,
          highlightIndex: -1,
        },
        '2': {
          tagName: 'input',
          attributes: { id: 'password', type: 'password', placeholder: 'Password' },
          xpath: '//*[@id="password"]',
          children: [],
          bbox: { x: 10, y: 60, width: 280, height: 40 },
          isVisible: true,
          isTopElement: true,
          isInteractive: true,
          highlightIndex: -1,
        },
        '3': {
          tagName: 'button',
          attributes: { id: 'login-btn', type: 'submit' },
          xpath: '//*[@id="login-btn"]',
          children: [],
          bbox: { x: 10, y: 110, width: 100, height: 40 },
          isVisible: true,
          isTopElement: true,
          isInteractive: true,
          highlightIndex: -1,
        }
      }
    };

    const uploadResponse = await request.post(`${baseURL}/upload/dom`, {
      data: { domTree }
    });
    const uploadResult = await uploadResponse.json();
    const domId = uploadResult.domId;

    // Generate plan
    const planRequest = {
      userInstruction: '填写用户名"testuser"，密码"password123"，然后点击登录按钮',
      conversationHistory: [],
      size: { width: 1920, height: 1080 },
      domId: domId,
      sessionId: 'test-session-api'
    };

    const response = await request.post(`${baseURL}/generate`, {
      data: planRequest
    });

    expect(response.ok()).toBeTruthy();
    
    const plan = await response.json();
    expect(plan.planId).toBeTruthy();
    expect(plan.actions).toBeTruthy();
    expect(Array.isArray(plan.actions)).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(0);

    // Verify action structure
    plan.actions.forEach((action: any) => {
      expect(action.id).toBeTruthy();
      expect(action.type).toBeTruthy();
      expect(typeof action.id).toBe('string');
      expect(typeof action.type).toBe('string');
    });
  });

  test('should upload execution results successfully', async ({ request }) => {
    const resultRequest = {
      planId: 'test-plan-' + Date.now(),
      results: [
        {
          step: 1,
          id: 'action-1',
          status: 'success',
          screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          log: 'Successfully located element'
        },
        {
          step: 2,
          id: 'action-2',
          status: 'success',
          screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          log: 'Successfully clicked button'
        }
      ],
      overallStatus: 'success'
    };

    const response = await request.post(`${baseURL}/result`, {
      data: resultRequest
    });

    expect(response.ok()).toBeTruthy();
    
    const result = await response.json();
    expect(result.success).toBe(true);
    expect(result.message).toBeTruthy();
  });

  test('should handle invalid DOM tree upload', async ({ request }) => {
    const invalidDomTree = {
      // Missing required fields
      rootId: '',
      map: {}
    };

    const response = await request.post(`${baseURL}/upload/dom`, {
      data: { domTree: invalidDomTree }
    });

    // Should handle invalid data gracefully
    if (!response.ok()) {
      expect(response.status()).toBeGreaterThanOrEqual(400);
    } else {
      // If accepted, should return appropriate response
      const result = await response.json();
      expect(result).toBeTruthy();
    }
  });

  test('should handle invalid plan generation request', async ({ request }) => {
    const invalidRequest = {
      userInstruction: '', // Empty instruction
      conversationHistory: [],
      size: { width: 0, height: 0 }, // Invalid size
      domId: 'non-existent-dom-id'
    };

    const response = await request.post(`${baseURL}/generate`, {
      data: invalidRequest
    });

    // Should handle invalid request appropriately
    if (!response.ok()) {
      expect(response.status()).toBeGreaterThanOrEqual(400);
    }
  });

  test('should handle large DOM tree upload', async ({ request }) => {
    // Generate a large DOM tree
    const largeMap: Record<string, any> = {};
    const rootId = '0';
    
    // Create 1000 elements
    for (let i = 0; i < 1000; i++) {
      largeMap[i.toString()] = {
        tagName: i % 2 === 0 ? 'div' : 'span',
        attributes: { 
          id: `element-${i}`,
          class: `class-${i % 10}`
        },
        xpath: `//*[@id="element-${i}"]`,
        children: i < 999 ? [(i + 1).toString()] : [],
        bbox: { x: i % 100, y: Math.floor(i / 100) * 20, width: 100, height: 20 },
        isVisible: true,
        isTopElement: true,
        isInteractive: i % 5 === 0,
        highlightIndex: -1,
      };
    }

    const largeDomTree = {
      rootId,
      map: largeMap
    };

    const startTime = Date.now();
    const response = await request.post(`${baseURL}/upload/dom`, {
      data: { domTree: largeDomTree }
    });
    const uploadTime = Date.now() - startTime;

    expect(response.ok()).toBeTruthy();
    
    const result = await response.json();
    expect(result.domId).toBeTruthy();
    expect(result.success).toBe(true);
    
    // Verify upload was reasonably fast (less than 30 seconds)
    expect(uploadTime).toBeLessThan(30000);
  });

  test('should handle concurrent requests', async ({ request }) => {
    const domTree = {
      rootId: '0',
      map: {
        '0': {
          tagName: 'button',
          attributes: { id: 'concurrent-btn' },
          xpath: '//*[@id="concurrent-btn"]',
          children: [],
          bbox: { x: 0, y: 0, width: 100, height: 30 },
          isVisible: true,
          isTopElement: true,
          isInteractive: true,
          highlightIndex: -1,
        }
      }
    };

    // Send multiple concurrent requests
    const promises = Array.from({ length: 5 }, async (_, index) => {
      const response = await request.post(`${baseURL}/upload/dom`, {
        data: { 
          domTree: {
            ...domTree,
            rootId: `${index}`
          }
        }
      });
      return response.json();
    });

    const results = await Promise.all(promises);

    // All requests should succeed
    results.forEach(result => {
      expect(result.domId).toBeTruthy();
      expect(result.success).toBe(true);
    });

    // All DOM IDs should be unique
    const domIds = results.map(r => r.domId);
    const uniqueDomIds = new Set(domIds);
    expect(uniqueDomIds.size).toBe(domIds.length);
  });

  test('should validate request content types', async ({ request }) => {
    // Test with invalid content type
    const response = await request.post(`${baseURL}/upload/dom`, {
      data: 'invalid-json-string',
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    // Should reject invalid content type
    expect(response.status()).toBeGreaterThanOrEqual(400);
  });

  test('should handle request timeouts gracefully', async ({ request }) => {
    // This test simulates a timeout scenario
    // In a real scenario, you might need to mock slow responses
    
    const domTree = {
      rootId: '0',
      map: {
        '0': {
          tagName: 'div',
          attributes: { id: 'timeout-test' },
          xpath: '//*[@id="timeout-test"]',
          children: [],
          bbox: { x: 0, y: 0, width: 100, height: 50 },
          isVisible: true,
          isTopElement: true,
          isInteractive: false,
          highlightIndex: -1,
        }
      }
    };

    try {
      const response = await request.post(`${baseURL}/upload/dom`, {
        data: { domTree },
        timeout: 1000 // Very short timeout
      });

      // If it succeeds within timeout, that's also valid
      if (response.ok()) {
        const result = await response.json();
        expect(result.success).toBe(true);
      }
    } catch (error) {
      // Timeout error is expected and acceptable
      expect(error).toBeTruthy();
    }
  });
});
