import { test, expect } from '@playwright/test';
import { setupTestPage, extractDomTree, verifyServerHealth } from '../utils/test-helpers';
import { uploadDomTree } from '../../api';

test.describe('DOM Tree Extraction Tests', () => {
  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should extract DOM tree from simple HTML page', async ({ page }) => {
    const testHtml = `
      <div id="container">
        <h1>Test Page</h1>
        <button id="testBtn" class="btn">Click Me</button>
        <input id="testInput" type="text" placeholder="Enter text">
        <p>This is a test paragraph.</p>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    // Verify DOM tree structure
    expect(domTree.rootId).toBeTruthy();
    expect(Object.keys(domTree.map).length).toBeGreaterThan(0);

    // Find specific elements in the DOM tree
    const elements = Object.values(domTree.map);
    const buttonElement = elements.find((el: any) => 
      el.tagName === 'button' && el.attributes?.id === 'testBtn'
    );
    const inputElement = elements.find((el: any) => 
      el.tagName === 'input' && el.attributes?.id === 'testInput'
    );

    expect(buttonElement).toBeTruthy();
    expect(inputElement).toBeTruthy();
    expect(buttonElement.attributes.class).toBe('btn');
    expect(inputElement.attributes.type).toBe('text');
  });

  test('should extract DOM tree from complex nested structure', async ({ page }) => {
    const testHtml = `
      <div id="app">
        <header>
          <nav>
            <ul>
              <li><a href="#home">Home</a></li>
              <li><a href="#about">About</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </nav>
        </header>
        <main>
          <section id="content">
            <article>
              <h2>Article Title</h2>
              <p>Article content goes here.</p>
              <div class="actions">
                <button class="btn primary">Save</button>
                <button class="btn secondary">Cancel</button>
              </div>
            </article>
          </section>
          <aside>
            <div class="widget">
              <h3>Widget Title</h3>
              <form>
                <input type="email" placeholder="Email">
                <textarea placeholder="Message"></textarea>
                <button type="submit">Submit</button>
              </form>
            </div>
          </aside>
        </main>
        <footer>
          <p>&copy; 2024 Test Company</p>
        </footer>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    // Verify complex structure
    expect(domTree.rootId).toBeTruthy();
    expect(Object.keys(domTree.map).length).toBeGreaterThan(10);

    // Verify hierarchical relationships
    const elements = Object.values(domTree.map);
    const navElement = elements.find((el: any) => el.tagName === 'nav');
    const formElement = elements.find((el: any) => el.tagName === 'form');
    const buttons = elements.filter((el: any) => el.tagName === 'button');

    expect(navElement).toBeTruthy();
    expect(formElement).toBeTruthy();
    expect(buttons.length).toBe(3); // Save, Cancel, Submit
  });

  test('should handle DOM tree with dynamic content', async ({ page }) => {
    const testHtml = `
      <div id="dynamic-container">
        <button id="addBtn">Add Item</button>
        <ul id="itemList"></ul>
      </div>
      <script>
        document.getElementById('addBtn').addEventListener('click', function() {
          const list = document.getElementById('itemList');
          const item = document.createElement('li');
          item.textContent = 'Item ' + (list.children.length + 1);
          item.className = 'list-item';
          list.appendChild(item);
        });
      </script>
    `;

    await setupTestPage(page, { html: testHtml });

    // Extract initial DOM tree
    let domTree = await extractDomTree(page);
    const initialElementCount = Object.keys(domTree.map).length;

    // Add dynamic content
    await page.click('#addBtn');
    await page.click('#addBtn');
    await page.click('#addBtn');

    // Extract DOM tree after changes
    domTree = await extractDomTree(page);
    const finalElementCount = Object.keys(domTree.map).length;

    // Verify dynamic content was captured
    expect(finalElementCount).toBeGreaterThan(initialElementCount);

    const listItems = Object.values(domTree.map).filter((el: any) => 
      el.tagName === 'li' && el.attributes?.class === 'list-item'
    );
    expect(listItems.length).toBe(3);
  });

  test('should upload DOM tree to server successfully', async ({ page }) => {
    const testHtml = `
      <div id="upload-test">
        <h1>Upload Test Page</h1>
        <form id="testForm">
          <input type="text" name="username" placeholder="Username">
          <input type="password" name="password" placeholder="Password">
          <button type="submit">Login</button>
        </form>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    // Upload DOM tree to server
    const domId = await uploadDomTree(domTree);

    expect(domId).toBeTruthy();
    expect(typeof domId).toBe('string');
    expect(domId.length).toBeGreaterThan(0);
  });

  test('should handle large DOM trees efficiently', async ({ page }) => {
    // Generate a large DOM structure
    let largeHtml = '<div id="large-container">';
    for (let i = 0; i < 100; i++) {
      largeHtml += `
        <div class="section-${i}">
          <h3>Section ${i}</h3>
          <p>Content for section ${i}</p>
          <ul>
            ${Array.from({ length: 10 }, (_, j) => `<li>Item ${j}</li>`).join('')}
          </ul>
          <div class="actions">
            <button class="btn edit">Edit</button>
            <button class="btn delete">Delete</button>
          </div>
        </div>
      `;
    }
    largeHtml += '</div>';

    await setupTestPage(page, { html: largeHtml });

    const startTime = Date.now();
    const domTree = await extractDomTree(page);
    const extractionTime = Date.now() - startTime;

    // Verify large DOM tree was extracted
    expect(domTree.rootId).toBeTruthy();
    expect(Object.keys(domTree.map).length).toBeGreaterThan(1000);

    // Verify extraction was reasonably fast (less than 5 seconds)
    expect(extractionTime).toBeLessThan(5000);

    // Upload large DOM tree
    const uploadStartTime = Date.now();
    const domId = await uploadDomTree(domTree);
    const uploadTime = Date.now() - uploadStartTime;

    expect(domId).toBeTruthy();
    // Verify upload was reasonably fast (less than 10 seconds)
    expect(uploadTime).toBeLessThan(10000);
  });

  test('should extract correct element attributes and properties', async ({ page }) => {
    const testHtml = `
      <div id="attributes-test">
        <input id="textInput" type="text" value="default" class="form-control" data-testid="text-input" required>
        <button id="submitBtn" type="submit" class="btn btn-primary" disabled data-action="submit">Submit</button>
        <a id="testLink" href="https://example.com" target="_blank" rel="noopener">External Link</a>
        <img id="testImage" src="test.jpg" alt="Test Image" width="100" height="50">
        <select id="testSelect" multiple>
          <option value="1">Option 1</option>
          <option value="2" selected>Option 2</option>
          <option value="3">Option 3</option>
        </select>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const elements = Object.values(domTree.map);

    // Test input element attributes
    const inputElement = elements.find((el: any) => el.attributes?.id === 'textInput');
    expect(inputElement).toBeTruthy();
    expect(inputElement.attributes.type).toBe('text');
    expect(inputElement.attributes.class).toBe('form-control');
    expect(inputElement.attributes['data-testid']).toBe('text-input');
    expect(inputElement.attributes.required).toBe('');

    // Test button element attributes
    const buttonElement = elements.find((el: any) => el.attributes?.id === 'submitBtn');
    expect(buttonElement).toBeTruthy();
    expect(buttonElement.attributes.type).toBe('submit');
    expect(buttonElement.attributes.class).toBe('btn btn-primary');
    expect(buttonElement.attributes.disabled).toBe('');

    // Test link element attributes
    const linkElement = elements.find((el: any) => el.attributes?.id === 'testLink');
    expect(linkElement).toBeTruthy();
    expect(linkElement.attributes.href).toBe('https://example.com');
    expect(linkElement.attributes.target).toBe('_blank');
    expect(linkElement.attributes.rel).toBe('noopener');

    // Test image element attributes
    const imageElement = elements.find((el: any) => el.attributes?.id === 'testImage');
    expect(imageElement).toBeTruthy();
    expect(imageElement.attributes.src).toBe('test.jpg');
    expect(imageElement.attributes.alt).toBe('Test Image');
    expect(imageElement.attributes.width).toBe('100');
    expect(imageElement.attributes.height).toBe('50');
  });
});
