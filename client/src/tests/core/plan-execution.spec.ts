import { test, expect } from '@playwright/test';
import { setupTestPage, extractDomTree, verifyServerHealth } from '../utils/test-helpers';
import { uploadDomTree, requestPlan, uploadResult } from '../../api';
import { executePlan } from '../../executor';

test.describe('Plan Execution Tests', () => {
  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should execute simple button click plan', async ({ page }) => {
    const testHtml = `
      <div id="execution-test">
        <h1>Execution Test Page</h1>
        <button id="clickBtn" onclick="this.textContent='Clicked!'">Click Me</button>
        <div id="result"></div>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan with Playwright page context
    const executionResult = await executePlan(plan, { page, timeout: 10000 });

    expect(executionResult).toBeTruthy();
    expect(executionResult.overallStatus).toBe('success');
    expect(executionResult.results.length).toBeGreaterThan(0);

    // Verify each step result
    executionResult.results.forEach(result => {
      expect(result.step).toBeGreaterThan(0);
      expect(result.id).toBeTruthy();
      expect(result.status).toBe('success');
      expect(result.log).toBeTruthy();
    });

    // Upload execution results
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus,
      executionResult.errorMessage
    );
  });

  test('should execute form filling plan', async ({ page }) => {
    const testHtml = `
      <div id="form-execution-test">
        <form id="testForm">
          <div>
            <label for="username">Username:</label>
            <input id="username" type="text" name="username">
          </div>
          <div>
            <label for="email">Email:</label>
            <input id="email" type="email" name="email">
          </div>
          <div>
            <label for="message">Message:</label>
            <textarea id="message" name="message"></textarea>
          </div>
          <button type="submit" id="submitBtn">Submit</button>
        </form>
        <div id="formResult"></div>
      </div>
      <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
          e.preventDefault();
          const formData = new FormData(this);
          document.getElementById('formResult').innerHTML = 
            'Form submitted with: ' + Array.from(formData.entries()).map(([k,v]) => k + '=' + v).join(', ');
        });
      </script>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '填写表单：用户名"testuser"，邮箱"<EMAIL>"，消息"Hello World"，然后提交',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan
    const executionResult = await executePlan(plan, { page, timeout: 15000 });

    expect(executionResult.overallStatus).toBe('success');

    // Verify form was filled correctly
    const usernameValue = await page.inputValue('#username');
    const emailValue = await page.inputValue('#email');
    const messageValue = await page.inputValue('#message');

    // Values should be filled (exact values depend on plan generation)
    expect(usernameValue.length).toBeGreaterThan(0);
    expect(emailValue.length).toBeGreaterThan(0);
    expect(messageValue.length).toBeGreaterThan(0);

    // Upload execution results
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus
    );
  });

  test('should handle execution errors gracefully', async ({ page }) => {
    const testHtml = `
      <div id="error-test">
        <button id="validBtn">Valid Button</button>
        <!-- Missing button that might be referenced in plan -->
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击一个可能不存在的按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan (might fail)
    const executionResult = await executePlan(plan, { page, timeout: 10000 });

    expect(executionResult).toBeTruthy();
    expect(executionResult.results.length).toBeGreaterThan(0);

    // Check if execution handled errors properly
    if (executionResult.overallStatus === 'failed') {
      expect(executionResult.errorMessage).toBeTruthy();
      
      // Verify failed steps have proper error information
      const failedSteps = executionResult.results.filter(r => r.status === 'failed');
      failedSteps.forEach(step => {
        expect(step.log).toBeTruthy();
        expect(step.log.length).toBeGreaterThan(0);
      });
    }

    // Upload execution results (including failures)
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus,
      executionResult.errorMessage
    );
  });

  test('should execute multi-step workflow plan', async ({ page }) => {
    const testHtml = `
      <div id="workflow-test">
        <div id="step1" class="step">
          <h2>Step 1</h2>
          <input id="input1" type="text" placeholder="Enter value 1">
          <button id="next1" onclick="showStep(2)">Next</button>
        </div>
        <div id="step2" class="step" style="display: none;">
          <h2>Step 2</h2>
          <select id="select1">
            <option value="">Choose option</option>
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </select>
          <button id="next2" onclick="showStep(3)">Next</button>
        </div>
        <div id="step3" class="step" style="display: none;">
          <h2>Step 3</h2>
          <p>Review your choices:</p>
          <div id="review"></div>
          <button id="finish" onclick="complete()">Finish</button>
        </div>
        <div id="completion" style="display: none;">
          <h2>Completed!</h2>
          <p>Workflow finished successfully.</p>
        </div>
      </div>
      <script>
        function showStep(stepNum) {
          document.querySelectorAll('.step').forEach(s => s.style.display = 'none');
          document.getElementById('step' + stepNum).style.display = 'block';
          
          if (stepNum === 3) {
            const input1Value = document.getElementById('input1').value;
            const select1Value = document.getElementById('select1').value;
            document.getElementById('review').innerHTML = 
              'Input: ' + input1Value + '<br>Selection: ' + select1Value;
          }
        }
        
        function complete() {
          document.querySelectorAll('.step').forEach(s => s.style.display = 'none');
          document.getElementById('completion').style.display = 'block';
        }
      </script>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '完成三步工作流程：第一步输入"test value"，第二步选择"Option 1"，第三步完成流程',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan
    const executionResult = await executePlan(plan, { page, timeout: 20000 });

    expect(executionResult).toBeTruthy();
    expect(executionResult.results.length).toBeGreaterThan(2);

    // Verify workflow progression
    if (executionResult.overallStatus === 'success') {
      // Check if completion message is visible
      const completionVisible = await page.isVisible('#completion');
      if (completionVisible) {
        const completionText = await page.textContent('#completion');
        expect(completionText).toContain('Completed');
      }
    }

    // Upload execution results
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus,
      executionResult.errorMessage
    );
  });

  test('should capture screenshots during execution', async ({ page }) => {
    const testHtml = `
      <div id="screenshot-test">
        <h1>Screenshot Test</h1>
        <button id="changeBtn" onclick="changeContent()">Change Content</button>
        <div id="content">Original Content</div>
      </div>
      <script>
        function changeContent() {
          document.getElementById('content').innerHTML = 'Content Changed!';
          document.getElementById('content').style.color = 'green';
        }
      </script>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击按钮改变内容',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan
    const executionResult = await executePlan(plan, { page, timeout: 10000 });

    expect(executionResult).toBeTruthy();

    // Verify screenshots were captured
    executionResult.results.forEach(result => {
      if (result.screenshot) {
        expect(result.screenshot).toBeTruthy();
        expect(typeof result.screenshot).toBe('string');
        // Should be a base64 encoded image or file path
        expect(result.screenshot.length).toBeGreaterThan(0);
      }
    });

    // Upload execution results
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus
    );
  });

  test('should handle different action types correctly', async ({ page }) => {
    const testHtml = `
      <div id="action-types-test">
        <h1>Action Types Test</h1>
        
        <!-- Click/Tap actions -->
        <button id="clickBtn">Click Me</button>
        
        <!-- Input actions -->
        <input id="textInput" type="text" placeholder="Type here">
        
        <!-- Scroll actions -->
        <div id="scrollContainer" style="height: 100px; overflow-y: scroll;">
          <div style="height: 300px;">
            <p>Scroll to see this content</p>
            <p style="margin-top: 200px;">Bottom content</p>
          </div>
        </div>
        
        <!-- Keyboard actions -->
        <input id="keyboardInput" type="text" placeholder="Press keys here">
        
        <!-- Drag actions (if supported) -->
        <div id="dragSource" draggable="true" style="width: 50px; height: 50px; background: blue;">Drag</div>
        <div id="dragTarget" style="width: 100px; height: 100px; border: 2px dashed gray;">Drop here</div>
        
        <div id="results"></div>
      </div>
      <script>
        document.getElementById('clickBtn').onclick = () => {
          document.getElementById('results').innerHTML += 'Button clicked<br>';
        };
        
        document.getElementById('textInput').oninput = (e) => {
          document.getElementById('results').innerHTML += 'Text input: ' + e.target.value + '<br>';
        };
        
        document.getElementById('keyboardInput').onkeydown = (e) => {
          document.getElementById('results').innerHTML += 'Key pressed: ' + e.key + '<br>';
        };
      </script>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击按钮，在文本框输入"hello"，然后在键盘输入框按回车键',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();

    // Execute the plan
    const executionResult = await executePlan(plan, { page, timeout: 15000 });

    expect(executionResult).toBeTruthy();

    // Verify different action types were executed
    const actionTypes = executionResult.results.map(r => 
      plan.actions.find(a => a.id === r.id)?.type
    ).filter(Boolean);

    // Should contain various action types
    expect(actionTypes.length).toBeGreaterThan(0);

    // Upload execution results
    await uploadResult(
      plan.planId,
      executionResult.results,
      executionResult.overallStatus,
      executionResult.errorMessage
    );
  });
});
