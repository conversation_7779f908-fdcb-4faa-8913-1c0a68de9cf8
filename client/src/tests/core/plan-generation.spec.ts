import { test, expect } from '@playwright/test';
import { setupTestPage, extractDomTree, verifyServerHealth } from '../utils/test-helpers';
import { uploadDomTree, requestPlan } from '../../api';

test.describe('Plan Generation Tests', () => {
  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should generate plan for simple button click', async ({ page }) => {
    const testHtml = `
      <div id="simple-test">
        <h1>Simple Test Page</h1>
        <button id="loginBtn" class="btn">Login</button>
        <button id="signupBtn" class="btn">Sign Up</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击登录按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.planId).toBeTruthy();
    expect(plan.actions).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(0);

    // Verify plan contains appropriate actions
    const hasLocateAction = plan.actions.some(action => action.type === 'Locate');
    const hasTapAction = plan.actions.some(action => action.type === 'Tap');
    
    expect(hasLocateAction || hasTapAction).toBeTruthy();
  });

  test('should generate plan for form filling', async ({ page }) => {
    const testHtml = `
      <div id="form-test">
        <h1>Login Form</h1>
        <form id="loginForm">
          <div>
            <label for="username">Username:</label>
            <input id="username" type="text" name="username" placeholder="Enter username">
          </div>
          <div>
            <label for="password">Password:</label>
            <input id="password" type="password" name="password" placeholder="Enter password">
          </div>
          <button type="submit" id="submitBtn">Submit</button>
        </form>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '填写用户名"testuser"，密码"password123"，然后点击提交按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(2);

    // Verify plan contains input actions
    const inputActions = plan.actions.filter(action => action.type === 'Input');
    const tapActions = plan.actions.filter(action => action.type === 'Tap');

    expect(inputActions.length).toBeGreaterThanOrEqual(2); // Username and password
    expect(tapActions.length).toBeGreaterThanOrEqual(1); // Submit button
  });

  test('should generate plan for complex multi-step workflow', async ({ page }) => {
    const testHtml = `
      <div id="complex-test">
        <nav>
          <ul>
            <li><a href="#products" id="productsLink">Products</a></li>
            <li><a href="#cart" id="cartLink">Cart</a></li>
          </ul>
        </nav>
        <main>
          <section id="products">
            <div class="product" data-id="1">
              <h3>Product 1</h3>
              <p>Price: $10</p>
              <button class="add-to-cart" data-product="1">Add to Cart</button>
            </div>
            <div class="product" data-id="2">
              <h3>Product 2</h3>
              <p>Price: $20</p>
              <button class="add-to-cart" data-product="2">Add to Cart</button>
            </div>
          </section>
          <section id="cart" style="display: none;">
            <h2>Shopping Cart</h2>
            <div id="cartItems"></div>
            <button id="checkoutBtn">Checkout</button>
          </section>
        </main>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '将Product 1添加到购物车，然后查看购物车并进行结账',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(2);

    // Verify plan contains multiple action types
    const actionTypes = plan.actions.map(action => action.type);
    expect(actionTypes).toContain('Tap'); // For clicking buttons/links
  });

  test('should generate plan with proper action sequence', async ({ page }) => {
    const testHtml = `
      <div id="sequence-test">
        <div id="step1" class="step active">
          <h2>Step 1: Enter Email</h2>
          <input id="email" type="email" placeholder="Enter your email">
          <button id="nextBtn1">Next</button>
        </div>
        <div id="step2" class="step hidden">
          <h2>Step 2: Enter Details</h2>
          <input id="firstName" type="text" placeholder="First Name">
          <input id="lastName" type="text" placeholder="Last Name">
          <button id="nextBtn2">Next</button>
        </div>
        <div id="step3" class="step hidden">
          <h2>Step 3: Confirm</h2>
          <p>Please confirm your information</p>
          <button id="confirmBtn">Confirm</button>
        </div>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '完成三步注册流程：输入邮箱"<EMAIL>"，然后输入姓名"John Doe"，最后确认信息',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(4);

    // Verify actions have proper sequence
    plan.actions.forEach((action, index) => {
      expect(action.id).toBeTruthy();
      expect(action.type).toBeTruthy();
      
      if (action.thought) {
        expect(typeof action.thought).toBe('string');
      }
    });
  });

  test('should handle different instruction languages', async ({ page }) => {
    const testHtml = `
      <div id="language-test">
        <button id="enBtn">English Button</button>
        <button id="zhBtn">中文按钮</button>
        <input id="textInput" type="text" placeholder="Enter text">
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    // Test Chinese instruction
    const chinesePlan = await requestPlan(
      '点击中文按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(chinesePlan).toBeTruthy();
    expect(chinesePlan.actions.length).toBeGreaterThan(0);

    // Test English instruction
    const englishPlan = await requestPlan(
      'Click the English button',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(englishPlan).toBeTruthy();
    expect(englishPlan.actions.length).toBeGreaterThan(0);
  });

  test('should generate plan with conversation history context', async ({ page }) => {
    const testHtml = `
      <div id="context-test">
        <div id="userProfile">
          <h2>User Profile</h2>
          <p>Name: <span id="userName">John Doe</span></p>
          <button id="editBtn">Edit Profile</button>
        </div>
        <div id="editForm" style="display: none;">
          <input id="nameInput" type="text" value="John Doe">
          <button id="saveBtn">Save</button>
          <button id="cancelBtn">Cancel</button>
        </div>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const conversationHistory = [
      { role: 'user' as const, content: '我想修改我的个人资料' },
      { role: 'assistant' as const, content: '好的，我来帮您找到编辑按钮' },
      { role: 'user' as const, content: '我想把名字改成"Jane Smith"' }
    ];

    const plan = await requestPlan(
      '现在点击编辑按钮，然后修改名字',
      conversationHistory,
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(1);

    // Verify plan considers the conversation context
    const hasEditAction = plan.actions.some(action => 
      action.thought?.includes('编辑') || action.thought?.includes('edit')
    );
    expect(hasEditAction).toBeTruthy();
  });

  test('should handle plan generation errors gracefully', async ({ page }) => {
    const testHtml = `<div id="empty-test"></div>`;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    // Test with impossible instruction
    try {
      const plan = await requestPlan(
        '点击一个不存在的按钮',
        [],
        { width: 1920, height: 1080 },
        domId
      );

      // If plan is generated, it should handle the impossible instruction gracefully
      if (plan) {
        expect(plan.actions).toBeTruthy();
        // The plan might contain error handling or alternative actions
      }
    } catch (error) {
      // Error handling is also acceptable for impossible instructions
      expect(error).toBeTruthy();
    }
  });

  test('should generate plan with proper metadata', async ({ page }) => {
    const testHtml = `
      <div id="metadata-test">
        <button id="testBtn">Test Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);
    const domId = await uploadDomTree(domTree);

    const plan = await requestPlan(
      '点击测试按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.planId).toBeTruthy();
    expect(typeof plan.planId).toBe('string');
    
    // Verify each action has required properties
    plan.actions.forEach(action => {
      expect(action.id).toBeTruthy();
      expect(action.type).toBeTruthy();
      expect(typeof action.id).toBe('string');
      expect(typeof action.type).toBe('string');
    });
  });
});
