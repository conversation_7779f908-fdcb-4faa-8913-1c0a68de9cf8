import { test, expect } from '@playwright/test';
import { setupTestPage, extractDomTree, verifyServerHealth } from '../utils/test-helpers';

test.describe('Boundary Conditions Tests', () => {
  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should handle empty DOM tree', async ({ page }) => {
    const testHtml = `<!-- Empty page -->`;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    // Should handle empty DOM gracefully
    try {
      const domId = await uploadDomTree(domTree);
      expect(domId).toBeTruthy();

      // Try to generate plan for empty DOM
      const plan = await requestPlan(
        '点击按钮',
        [],
        { width: 1920, height: 1080 },
        domId
      );

      // Should either generate appropriate plan or handle gracefully
      if (plan) {
        expect(plan.actions).toBeTruthy();
      }
    } catch (error) {
      // Error handling for empty DOM is acceptable
      expect(error).toBeTruthy();
    }
  });

  test('should handle extremely large DOM tree', async ({ page }) => {
    // Generate a very large DOM structure
    let largeHtml = '<div id="large-container">';
    
    // Create 5000 nested elements
    for (let i = 0; i < 5000; i++) {
      largeHtml += `
        <div class="level-${i % 10}" data-index="${i}">
          <span>Element ${i}</span>
          <input type="text" id="input-${i}" value="value-${i}">
          <button id="btn-${i}" onclick="console.log('${i}')">Button ${i}</button>
        </div>
      `;
    }
    largeHtml += '</div>';

    await setupTestPage(page, { html: largeHtml });

    const startTime = Date.now();
    const domTree = await extractDomTree(page);
    const extractionTime = Date.now() - startTime;

    // Should handle large DOM within reasonable time
    expect(extractionTime).toBeLessThan(30000); // 30 seconds max
    expect(Object.keys(domTree.map).length).toBeGreaterThan(10000);

    const { uploadDomTree } = await import('../../api');

    const uploadStartTime = Date.now();
    try {
      const domId = await uploadDomTree(domTree);
      const uploadTime = Date.now() - uploadStartTime;

      expect(domId).toBeTruthy();
      expect(uploadTime).toBeLessThan(60000); // 60 seconds max for upload
    } catch (error) {
      // Large DOM might exceed size limits, which is acceptable
      expect(error).toBeTruthy();
      expect(error.message).toMatch(/size|limit|too large/i);
    }
  });

  test('should handle very long text content', async ({ page }) => {
    const longText = 'A'.repeat(100000); // 100KB of text
    const testHtml = `
      <div id="long-text-test">
        <h1>Long Text Test</h1>
        <p id="longParagraph">${longText}</p>
        <textarea id="longTextarea">${longText}</textarea>
        <input type="text" id="longInput" value="${longText.substring(0, 1000)}">
        <button id="submitBtn">Submit</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    try {
      const domId = await uploadDomTree(domTree);
      expect(domId).toBeTruthy();

      // Test plan generation with long text content
      const plan = await requestPlan(
        '在长文本区域输入新内容并提交',
        [],
        { width: 1920, height: 1080 },
        domId
      );

      if (plan) {
        expect(plan.actions.length).toBeGreaterThan(0);
      }
    } catch (error) {
      // Long text might exceed limits
      expect(error).toBeTruthy();
    }
  });

  test('should handle special characters and Unicode', async ({ page }) => {
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?`~';
    const unicodeChars = '你好世界 🌍 🚀 ñáéíóú αβγδε русский العربية';
    const emojiChars = '😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾';

    const testHtml = `
      <div id="special-chars-test">
        <h1>Special Characters: ${specialChars}</h1>
        <p>Unicode: ${unicodeChars}</p>
        <div>Emojis: ${emojiChars}</div>
        <input type="text" id="specialInput" placeholder="${specialChars}">
        <textarea id="unicodeTextarea">${unicodeChars}</textarea>
        <button id="emojiBtn" title="${emojiChars}">Submit ${emojiChars}</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    const domId = await uploadDomTree(domTree);
    expect(domId).toBeTruthy();

    // Test plan generation with special characters
    const plan = await requestPlan(
      `输入特殊字符"${specialChars}"和Unicode文本"${unicodeChars}"`,
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(0);
  });

  test('should handle zero and negative dimensions', async ({ page }) => {
    const testHtml = `
      <div id="dimensions-test">
        <div style="width: 0; height: 0;">Zero size element</div>
        <div style="width: -10px; height: -10px;">Negative size element</div>
        <div style="position: absolute; left: -1000px; top: -1000px;">Off-screen element</div>
        <button id="testBtn">Test Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    const domId = await uploadDomTree(domTree);
    expect(domId).toBeTruthy();

    // Test with zero dimensions
    try {
      const plan = await requestPlan(
        '点击按钮',
        [],
        { width: 0, height: 0 },
        domId
      );

      if (plan) {
        expect(plan.actions).toBeTruthy();
      }
    } catch (error) {
      // Zero dimensions might be rejected
      expect(error).toBeTruthy();
    }

    // Test with negative dimensions
    try {
      const plan = await requestPlan(
        '点击按钮',
        [],
        { width: -100, height: -100 },
        domId
      );

      if (plan) {
        expect(plan.actions).toBeTruthy();
      }
    } catch (error) {
      // Negative dimensions might be rejected
      expect(error).toBeTruthy();
    }
  });

  test('should handle extremely long instructions', async ({ page }) => {
    const testHtml = `
      <div id="long-instruction-test">
        <button id="btn1">Button 1</button>
        <button id="btn2">Button 2</button>
        <input id="input1" type="text">
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    const domId = await uploadDomTree(domTree);

    // Create extremely long instruction (10KB)
    const longInstruction = '请执行以下复杂的操作序列：'.repeat(500) + 
      '首先点击按钮1，然后在输入框中输入文本，最后点击按钮2完成操作。';

    try {
      const plan = await requestPlan(
        longInstruction,
        [],
        { width: 1920, height: 1080 },
        domId
      );

      if (plan) {
        expect(plan.actions.length).toBeGreaterThan(0);
      }
    } catch (error) {
      // Long instructions might exceed limits
      expect(error).toBeTruthy();
      expect(error.message).toMatch(/length|limit|too long/i);
    }
  });

  test('should handle maximum conversation history', async ({ page }) => {
    const testHtml = `
      <div id="conversation-test">
        <button id="conversationBtn">Conversation Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    const domId = await uploadDomTree(domTree);

    // Create very long conversation history (1000 messages)
    const longConversationHistory = [];
    for (let i = 0; i < 1000; i++) {
      longConversationHistory.push({
        role: i % 2 === 0 ? 'user' as const : 'assistant' as const,
        content: `Message ${i}: This is a test message with some content.`
      });
    }

    try {
      const plan = await requestPlan(
        '点击按钮',
        longConversationHistory,
        { width: 1920, height: 1080 },
        domId
      );

      if (plan) {
        expect(plan.actions.length).toBeGreaterThan(0);
      }
    } catch (error) {
      // Long conversation history might exceed limits
      expect(error).toBeTruthy();
    }
  });

  test('should handle malformed HTML structures', async ({ page }) => {
    const malformedHtml = `
      <div id="malformed-test">
        <p>Unclosed paragraph
        <div>
          <span>Nested without closing</div>
        </span>
        <button onclick="alert('test')" id="malformedBtn">Button</button>
        <input type="text" value="test" unclosed-attribute>
        <!-- Malformed comment --
        <script>console.log('test');</script>
      </div>
    `;

    await setupTestPage(page, { html: malformedHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    // Should handle malformed HTML gracefully
    const domId = await uploadDomTree(domTree);
    expect(domId).toBeTruthy();

    const plan = await requestPlan(
      '点击按钮',
      [],
      { width: 1920, height: 1080 },
      domId
    );

    expect(plan).toBeTruthy();
    expect(plan.actions.length).toBeGreaterThan(0);
  });

  test('should handle null and undefined values', async ({ page }) => {
    const testHtml = `
      <div id="null-test">
        <button id="nullBtn">Null Test Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    const domId = await uploadDomTree(domTree);

    // Test with null/undefined values
    try {
      const plan = await requestPlan(
        null as any,
        [],
        { width: 1920, height: 1080 },
        domId
      );
    } catch (error) {
      expect(error).toBeTruthy();
    }

    try {
      const plan = await requestPlan(
        '点击按钮',
        null as any,
        { width: 1920, height: 1080 },
        domId
      );
    } catch (error) {
      expect(error).toBeTruthy();
    }

    try {
      const plan = await requestPlan(
        '点击按钮',
        [],
        null as any,
        domId
      );
    } catch (error) {
      expect(error).toBeTruthy();
    }
  });

  test('should handle circular references in DOM data', async ({ page }) => {
    const testHtml = `
      <div id="circular-test">
        <button id="circularBtn">Circular Test Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    let domTree = await extractDomTree(page);

    // Artificially create circular reference
    const circularDomTree = {
      rootId: domTree.rootId,
      map: { ...domTree.map }
    };

    // Add circular reference (this would normally be prevented by JSON.stringify)
    // But we test the server's handling of such data
    const { uploadDomTree } = await import('../../api');

    try {
      const domId = await uploadDomTree(circularDomTree);
      expect(domId).toBeTruthy();
    } catch (error) {
      // Circular references should be handled gracefully
      expect(error).toBeTruthy();
    }
  });
});
