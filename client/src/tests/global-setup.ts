import { chromium, FullConfig } from '@playwright/test';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

/**
 * Global setup for E2E tests
 * Initializes database, starts services, and prepares test environment
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for Browser RPA E2E tests...');

  try {
    // 1. Initialize database
    console.log('📊 Initializing test database...');
    const serverPath = path.resolve(__dirname, '../../../server');
    
    // Reset and initialize database
    await execAsync('npm run db:reset', { cwd: serverPath });
    await execAsync('npm run db:init', { cwd: serverPath });
    
    console.log('✅ Database initialized successfully');

    // 2. Wait for server to be ready
    console.log('⏳ Waiting for server to be ready...');
    await waitForServer('http://localhost:3000/api/plan/health', 60000);
    console.log('✅ Server is ready');

    // 3. Seed test data if needed
    console.log('🌱 Seeding test data...');
    await seedTestData();
    console.log('✅ Test data seeded');

    // 4. Setup test browser context
    console.log('🌐 Setting up test browser context...');
    const browser = await chromium.launch();
    const context = await browser.newContext();
    
    // Store browser context for tests
    (global as any).testBrowser = browser;
    (global as any).testContext = context;
    
    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}

/**
 * Wait for server to be ready
 */
async function waitForServer(url: string, timeout: number): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return;
      }
    } catch (error) {
      // Server not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`Server not ready after ${timeout}ms`);
}

/**
 * Seed test data
 */
async function seedTestData(): Promise<void> {
  try {
    // Create test session
    const sessionResponse = await fetch('http://localhost:3000/api/plan/session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId: 'test-session-001',
        userId: 'test-user',
        screenWidth: 1920,
        screenHeight: 1080,
        userAgent: 'Playwright Test Agent'
      })
    });

    if (!sessionResponse.ok) {
      console.warn('⚠️ Failed to create test session, continuing...');
    }

    // Add more test data as needed
    console.log('📝 Test session created');

  } catch (error) {
    console.warn('⚠️ Failed to seed test data:', error);
    // Don't fail setup if seeding fails
  }
}

export default globalSetup;
