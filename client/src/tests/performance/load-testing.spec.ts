import { test, expect } from '@playwright/test';
import { setupTestPage, extractDomTree, verifyServerHealth } from '../utils/test-helpers';

test.describe('Performance and Load Testing', () => {
  test.beforeEach(async () => {
    await verifyServerHealth();
  });

  test('should handle concurrent DOM uploads efficiently', async ({ page, browser }) => {
    const testHtml = `
      <div id="concurrent-upload-test">
        <h1>Concurrent Upload Test</h1>
        <button id="btn1">Button 1</button>
        <button id="btn2">Button 2</button>
        <input id="input1" type="text" placeholder="Input 1">
        <input id="input2" type="text" placeholder="Input 2">
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree } = await import('../../api');

    // Create multiple concurrent upload requests
    const concurrentRequests = 10;
    const uploadPromises = [];

    const startTime = Date.now();

    for (let i = 0; i < concurrentRequests; i++) {
      // Slightly modify DOM tree for each request to make them unique
      const modifiedDomTree = {
        ...domTree,
        requestId: `request-${i}`
      };
      uploadPromises.push(uploadDomTree(modifiedDomTree));
    }

    const results = await Promise.allSettled(uploadPromises);
    const endTime = Date.now();

    const totalTime = endTime - startTime;
    const successfulUploads = results.filter(r => r.status === 'fulfilled').length;

    // Performance expectations
    expect(successfulUploads).toBeGreaterThan(0);
    expect(totalTime).toBeLessThan(30000); // Should complete within 30 seconds
    
    // Average time per request should be reasonable
    const avgTimePerRequest = totalTime / concurrentRequests;
    expect(avgTimePerRequest).toBeLessThan(10000); // Less than 10 seconds per request on average

    console.log(`Concurrent uploads: ${successfulUploads}/${concurrentRequests} successful in ${totalTime}ms`);
  });

  test('should handle concurrent plan generation requests', async ({ page }) => {
    const testHtml = `
      <div id="concurrent-plan-test">
        <form id="testForm">
          <input id="username" type="text" name="username">
          <input id="password" type="password" name="password">
          <button type="submit" id="submitBtn">Submit</button>
        </form>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    // Upload DOM tree first
    const domId = await uploadDomTree(domTree);

    // Create multiple concurrent plan generation requests
    const concurrentRequests = 5; // Fewer requests for plan generation due to LLM costs
    const planPromises = [];

    const instructions = [
      '填写用户名"user1"和密码"pass1"',
      '填写用户名"user2"和密码"pass2"',
      '填写用户名"user3"和密码"pass3"',
      '填写用户名"user4"和密码"pass4"',
      '填写用户名"user5"和密码"pass5"'
    ];

    const startTime = Date.now();

    for (let i = 0; i < concurrentRequests; i++) {
      planPromises.push(
        requestPlan(
          instructions[i],
          [],
          { width: 1920, height: 1080 },
          domId,
          undefined,
          `session-${i}`
        )
      );
    }

    const results = await Promise.allSettled(planPromises);
    const endTime = Date.now();

    const totalTime = endTime - startTime;
    const successfulPlans = results.filter(r => r.status === 'fulfilled').length;

    // Performance expectations for plan generation
    expect(successfulPlans).toBeGreaterThan(0);
    expect(totalTime).toBeLessThan(120000); // Should complete within 2 minutes

    // Verify plan quality
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const plan = result.value;
        expect(plan.planId).toBeTruthy();
        expect(plan.actions.length).toBeGreaterThan(0);
      }
    });

    console.log(`Concurrent plans: ${successfulPlans}/${concurrentRequests} successful in ${totalTime}ms`);
  });

  test('should maintain performance with large DOM trees', async ({ page }) => {
    // Generate progressively larger DOM trees and measure performance
    const sizes = [100, 500, 1000, 2000];

    for (const size of sizes) {
      let largeHtml = `<div id="large-dom-${size}">`;
      
      for (let i = 0; i < size; i++) {
        largeHtml += `
          <div class="item-${i}" data-index="${i}">
            <h3>Item ${i}</h3>
            <p>Description for item ${i}</p>
            <button id="btn-${i}">Action ${i}</button>
            <input type="text" id="input-${i}" value="value-${i}">
          </div>
        `;
      }
      largeHtml += '</div>';

      await setupTestPage(page, { html: largeHtml });

      // Measure DOM extraction time
      const extractStartTime = Date.now();
      const domTree = await extractDomTree(page);
      const extractTime = Date.now() - extractStartTime;

      expect(Object.keys(domTree.map).length).toBeGreaterThan(size);

      // Measure upload time
      const { uploadDomTree } = await import('../../api');
      
      const uploadStartTime = Date.now();
      const domId = await uploadDomTree(domTree);
      const uploadTime = Date.now() - uploadStartTime;

      expect(domId).toBeTruthy();

      // Performance should scale reasonably
      expect(extractTime).toBeLessThan(size * 10); // Max 10ms per element for extraction
      expect(uploadTime).toBeLessThan(60000); // Max 60 seconds for upload

      console.log(`DOM size ${size}: extract=${extractTime}ms, upload=${uploadTime}ms`);
    }
  });

  test('should handle rapid sequential requests', async ({ page }) => {
    const testHtml = `
      <div id="rapid-requests-test">
        <button id="rapidBtn">Rapid Test Button</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree } = await import('../../api');

    // Make rapid sequential requests
    const requestCount = 20;
    const results = [];
    const timings = [];

    for (let i = 0; i < requestCount; i++) {
      const startTime = Date.now();
      
      try {
        const domId = await uploadDomTree({
          ...domTree,
          sequenceId: i
        });
        const endTime = Date.now();
        
        results.push({ success: true, domId, time: endTime - startTime });
        timings.push(endTime - startTime);
      } catch (error) {
        const endTime = Date.now();
        results.push({ success: false, error, time: endTime - startTime });
        timings.push(endTime - startTime);
      }
    }

    const successfulRequests = results.filter(r => r.success).length;
    const avgResponseTime = timings.reduce((a, b) => a + b, 0) / timings.length;
    const maxResponseTime = Math.max(...timings);

    // Performance expectations
    expect(successfulRequests).toBeGreaterThan(requestCount * 0.8); // At least 80% success rate
    expect(avgResponseTime).toBeLessThan(5000); // Average response time under 5 seconds
    expect(maxResponseTime).toBeLessThan(30000); // Max response time under 30 seconds

    console.log(`Rapid requests: ${successfulRequests}/${requestCount} successful, avg=${avgResponseTime}ms, max=${maxResponseTime}ms`);
  });

  test('should handle memory-intensive operations', async ({ page }) => {
    const testHtml = `
      <div id="memory-test">
        <h1>Memory Intensive Test</h1>
      </div>
    `;

    // Create multiple large DOM trees in memory
    const domTrees = [];
    const { uploadDomTree } = await import('../../api');

    for (let i = 0; i < 10; i++) {
      await setupTestPage(page, { html: testHtml });
      
      // Generate large DOM tree
      let largeHtml = `<div id="memory-container-${i}">`;
      for (let j = 0; j < 1000; j++) {
        largeHtml += `<div class="item-${j}" data-value="${'x'.repeat(100)}">Item ${j}</div>`;
      }
      largeHtml += '</div>';

      await page.setContent(`<!DOCTYPE html><html><body>${largeHtml}</body></html>`);
      
      const domTree = await extractDomTree(page);
      domTrees.push(domTree);
    }

    // Upload all DOM trees
    const startTime = Date.now();
    const uploadPromises = domTrees.map((tree, index) => 
      uploadDomTree({ ...tree, batchId: index })
    );

    const results = await Promise.allSettled(uploadPromises);
    const endTime = Date.now();

    const successfulUploads = results.filter(r => r.status === 'fulfilled').length;
    const totalTime = endTime - startTime;

    expect(successfulUploads).toBeGreaterThan(5); // At least half should succeed
    expect(totalTime).toBeLessThan(300000); // Should complete within 5 minutes

    console.log(`Memory test: ${successfulUploads}/10 uploads successful in ${totalTime}ms`);
  });

  test('should maintain performance under sustained load', async ({ page }) => {
    const testHtml = `
      <div id="sustained-load-test">
        <form>
          <input type="text" id="testInput">
          <button type="submit" id="testSubmit">Submit</button>
        </form>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan } = await import('../../api');

    // Sustained load test - make requests over time
    const testDuration = 60000; // 1 minute
    const requestInterval = 2000; // Every 2 seconds
    const startTime = Date.now();
    const results = [];

    while (Date.now() - startTime < testDuration) {
      const iterationStart = Date.now();
      
      try {
        // Upload DOM
        const domId = await uploadDomTree({
          ...domTree,
          timestamp: Date.now()
        });

        // Generate plan
        const plan = await requestPlan(
          '填写表单并提交',
          [],
          { width: 1920, height: 1080 },
          domId,
          undefined,
          `sustained-session-${Date.now()}`
        );

        const iterationTime = Date.now() - iterationStart;
        results.push({ success: true, time: iterationTime });

      } catch (error) {
        const iterationTime = Date.now() - iterationStart;
        results.push({ success: false, time: iterationTime, error });
      }

      // Wait for next interval
      const elapsed = Date.now() - iterationStart;
      if (elapsed < requestInterval) {
        await new Promise(resolve => setTimeout(resolve, requestInterval - elapsed));
      }
    }

    const successfulRequests = results.filter(r => r.success).length;
    const totalRequests = results.length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;

    // Performance expectations for sustained load
    expect(successfulRequests).toBeGreaterThan(totalRequests * 0.7); // At least 70% success rate
    expect(avgResponseTime).toBeLessThan(15000); // Average response time under 15 seconds
    expect(totalRequests).toBeGreaterThan(10); // Should have made multiple requests

    console.log(`Sustained load: ${successfulRequests}/${totalRequests} successful, avg=${avgResponseTime}ms`);
  });

  test('should handle resource cleanup efficiently', async ({ page }) => {
    const testHtml = `
      <div id="cleanup-test">
        <button id="cleanupBtn">Cleanup Test</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });

    // Generate multiple requests to create data for cleanup
    const { uploadDomTree, requestPlan } = await import('../../api');
    const domTree = await extractDomTree(page);

    // Create test data
    const testDataPromises = [];
    for (let i = 0; i < 5; i++) {
      testDataPromises.push(
        uploadDomTree({ ...domTree, testId: i }).then(domId =>
          requestPlan(
            '点击按钮',
            [],
            { width: 1920, height: 1080 },
            domId,
            undefined,
            `cleanup-session-${i}`
          )
        )
      );
    }

    await Promise.allSettled(testDataPromises);

    // Test cleanup performance
    const cleanupStartTime = Date.now();
    
    const cleanupResponse = await fetch('http://localhost:3000/api/cleanup/run', {
      method: 'POST'
    });

    const cleanupTime = Date.now() - cleanupStartTime;
    
    expect(cleanupResponse.ok).toBeTruthy();
    
    const cleanupResult = await cleanupResponse.json();
    expect(cleanupResult.success).toBe(true);

    // Cleanup should be reasonably fast
    expect(cleanupTime).toBeLessThan(30000); // Under 30 seconds

    console.log(`Cleanup completed in ${cleanupTime}ms`);
  });

  test('should handle database performance under load', async ({ page }) => {
    const testHtml = `
      <div id="database-load-test">
        <button id="dbBtn">Database Load Test</button>
      </div>
    `;

    await setupTestPage(page, { html: testHtml });
    const domTree = await extractDomTree(page);

    const { uploadDomTree, requestPlan, uploadResult } = await import('../../api');

    // Create database load by generating many plans and results
    const loadTestPromises = [];
    const startTime = Date.now();

    for (let i = 0; i < 10; i++) {
      const promise = uploadDomTree({ ...domTree, dbLoadTest: i })
        .then(domId => requestPlan(
          `数据库负载测试 ${i}`,
          [],
          { width: 1920, height: 1080 },
          domId,
          undefined,
          `db-load-session-${i}`
        ))
        .then(plan => uploadResult(
          plan.planId,
          [
            {
              step: 1,
              id: 'test-action',
              status: 'success',
              screenshot: 'data:image/png;base64,test',
              log: `Database load test ${i} completed`
            }
          ],
          'success'
        ));

      loadTestPromises.push(promise);
    }

    const results = await Promise.allSettled(loadTestPromises);
    const endTime = Date.now();

    const successfulOperations = results.filter(r => r.status === 'fulfilled').length;
    const totalTime = endTime - startTime;

    expect(successfulOperations).toBeGreaterThan(5); // At least half should succeed
    expect(totalTime).toBeLessThan(180000); // Should complete within 3 minutes

    console.log(`Database load test: ${successfulOperations}/10 operations successful in ${totalTime}ms`);
  });
});
