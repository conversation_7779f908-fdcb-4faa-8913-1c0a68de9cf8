import { test, expect } from "@playwright/test";

test.describe("独立E2E测试 - 百度搜索", () => {
  test("应该能够执行完整的百度搜索流程", async ({ page }) => {
    console.log("🚀 开始执行独立E2E测试");
    console.log("📋 任务: 打开百度主页，搜索如何注册stripe，并获取前三个结果");
    
    // 1. 打开百度主页
    console.log("🌐 正在打开百度主页...");
    await page.goto("https://www.baidu.com", { 
      waitUntil: "networkidle",
      timeout: 30000 
    });
    
    // 验证页面加载成功
    await expect(page).toHaveTitle(/百度/);
    console.log("✅ 百度主页加载成功");
    
    // 2. 等待搜索框出现
    console.log("🔍 等待搜索框加载...");
    await page.waitForSelector("#kw", { timeout: 10000 });
    
    // 验证搜索框存在
    const searchInput = page.locator("#kw");
    await expect(searchInput).toBeVisible();
    console.log("✅ 搜索框已找到");
    
    // 3. 输入搜索关键词
    console.log("⌨️ 正在输入搜索关键词...");
    await searchInput.fill("如何注册stripe");
    
    // 验证输入成功
    await expect(searchInput).toHaveValue("如何注册stripe");
    console.log("✅ 搜索关键词输入成功");
    
    // 4. 点击搜索按钮
    console.log("🖱️ 正在点击搜索按钮...");
    const searchButton = page.locator("#su");
    await expect(searchButton).toBeVisible();
    await searchButton.click();
    
    // 5. 等待搜索结果页面加载
    console.log("⏳ 等待搜索结果加载...");
    await page.waitForLoadState("networkidle", { timeout: 15000 });
    
    // 验证已跳转到搜索结果页
    await expect(page).toHaveURL(/.*s\?.*wd=.*stripe.*/);
    console.log("✅ 已跳转到搜索结果页面");

    // 6. 尝试获取搜索结果
    console.log("🔍 正在获取搜索结果...");

    const resultSelectors = [
      ".result h3 a",
      ".c-container h3 a",
      ".c-container .t a",
      "[data-log] h3 a"
    ];

    let searchResults: any[] = [];

    for (const selector of resultSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        searchResults = await page.$$eval(selector, (links) =>
          links.slice(0, 3).map((link, index) => ({
            index: index + 1,
            title: link.textContent?.trim() || "",
            url: (link as HTMLAnchorElement).href || ""
          }))
        );

        if (searchResults.length > 0) {
          console.log(`✅ 找到 ${searchResults.length} 个搜索结果:`);
          searchResults.forEach(result => {
            console.log(`${result.index}. ${result.title}`);
            console.log(`   URL: ${result.url}`);
          });
          break;
        }
      } catch (error) {
        console.log(`⚠️ 选择器 "${selector}" 未找到结果`);
        continue;
      }
    }

    if (searchResults.length === 0) {
      console.log("⚠️ 未能获取到搜索结果，但搜索功能正常");
    }

    // 7. 截图保存
    await page.screenshot({
      path: "test-results/baidu-search-e2e.png",
      fullPage: true
    });
    console.log("📸 搜索结果截图已保存");

    console.log("🎉 独立E2E测试完成！");
  });
});
