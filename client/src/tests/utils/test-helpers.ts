import { Page, expect } from '@playwright/test';
import { buildDomTreeMap, requestPlan, uploadDomTree, uploadResult } from '../../api';
import type { GeneratePlanResponse, StepResult } from '../../api';

/**
 * Test utilities and helper functions for E2E tests
 */

export interface TestPageConfig {
  url?: string;
  html?: string;
  waitForSelector?: string;
  viewport?: { width: number; height: number };
}

export interface TestExecutionResult {
  domId: string;
  plan: GeneratePlanResponse;
  executionResults: {
    results: StepResult[];
    overallStatus: 'success' | 'failed';
    errorMessage?: string;
  };
}

/**
 * Setup a test page with custom HTML or navigate to URL
 */
export async function setupTestPage(page: Page, config: TestPageConfig): Promise<void> {
  if (config.viewport) {
    await page.setViewportSize(config.viewport);
  }

  if (config.html) {
    // Create a test page with custom HTML
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Test Page</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
            .input { padding: 8px; margin: 5px; border: 1px solid #ccc; }
            .hidden { display: none; }
            .disabled { opacity: 0.5; pointer-events: none; }
          </style>
        </head>
        <body>
          ${config.html}
        </body>
      </html>
    `;
    
    await page.setContent(htmlContent);
  } else if (config.url) {
    await page.goto(config.url);
  }

  if (config.waitForSelector) {
    await page.waitForSelector(config.waitForSelector);
  }

  // Wait for page to be fully loaded
  await page.waitForLoadState('networkidle');
}

/**
 * Extract DOM tree from current page
 */
export async function extractDomTree(page: Page): Promise<{ rootId: string; map: Record<string, any> }> {
  return await page.evaluate(() => {
    // Inline DOM tree builder for page context
    function buildDomTreeMap() {
      const map: Record<string, any> = {};
      let idCounter = 0;

      function traverse(node: any): string | null {
        if (!node) return null;
        const id = `${idCounter++}`;

        if (node.nodeType === Node.TEXT_NODE) {
          const text = node.textContent?.trim();
          if (!text) return null;
          map[id] = {
            type: 'TEXT_NODE',
            text,
            isVisible: true,
          };
          return id;
        }

        if (node.nodeType !== Node.ELEMENT_NODE) return null;
        const el = node as Element;
        const rect = (el as HTMLElement).getBoundingClientRect?.() || {
          x: 0, y: 0, width: 0, height: 0,
        };

        const childrenIds: string[] = [];
        for (const child of Array.from(el.childNodes)) {
          const childId = traverse(child);
          if (childId) childrenIds.push(childId);
        }

        function getXPath(element: any): string {
          if (element.id) return `//*[@id="${element.id}"]`;
          const parts: string[] = [];
          while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 1;
            let sibling = element.previousElementSibling;
            while (sibling) {
              if (sibling.tagName === element.tagName) index++;
              sibling = sibling.previousElementSibling;
            }
            parts.unshift(`${element.tagName.toLowerCase()}[${index}]`);
            element = element.parentElement;
          }
          return `/${parts.join('/')}`;
        }

        map[id] = {
          tagName: el.tagName.toLowerCase(),
          attributes: Object.fromEntries(
            Array.from(el.attributes).map((attr) => [attr.name, attr.value])
          ),
          xpath: getXPath(el),
          children: childrenIds,
          bbox: {
            x: rect.x || 0,
            y: rect.y || 0,
            width: rect.width || 0,
            height: rect.height || 0,
          },
          isVisible: true,
          isTopElement: true,
          isInteractive: false,
          highlightIndex: -1,
        };

        return id;
      }

      const rootId = traverse(document.body);
      return { rootId: rootId || '', map };
    }

    return buildDomTreeMap();
  });
}

/**
 * Execute complete RPA workflow: DOM extraction -> Plan generation -> Execution
 */
export async function executeRPAWorkflow(
  page: Page,
  userInstruction: string,
  sessionId: string = 'test-session'
): Promise<TestExecutionResult> {
  // 1. Extract DOM tree
  const domTree = await extractDomTree(page);
  expect(domTree.rootId).toBeTruthy();
  expect(Object.keys(domTree.map).length).toBeGreaterThan(0);

  // 2. Upload DOM tree
  const domId = await uploadDomTree(domTree);
  expect(domId).toBeTruthy();

  // 3. Generate plan
  const plan = await requestPlan(
    userInstruction,
    [],
    { width: 1920, height: 1080 },
    domId,
    undefined,
    sessionId
  );
  expect(plan).toBeTruthy();
  expect(plan.actions.length).toBeGreaterThan(0);

  // 4. Execute plan (mock execution for now)
  const executionResults = await mockExecutePlan(plan);

  // 5. Upload results
  await uploadResult(
    plan.planId,
    executionResults.results,
    executionResults.overallStatus,
    executionResults.errorMessage
  );

  return {
    domId,
    plan,
    executionResults
  };
}

/**
 * Mock plan execution for testing
 */
async function mockExecutePlan(plan: GeneratePlanResponse): Promise<{
  results: StepResult[];
  overallStatus: 'success' | 'failed';
  errorMessage?: string;
}> {
  const results: StepResult[] = [];
  
  for (let i = 0; i < plan.actions.length; i++) {
    const action = plan.actions[i];
    results.push({
      step: i + 1,
      id: action.id,
      status: 'success',
      screenshot: 'data:image/png;base64,mock-screenshot',
      log: `Successfully executed ${action.type}`,
    });
  }

  return {
    results,
    overallStatus: 'success'
  };
}

/**
 * Wait for API response with timeout
 */
export async function waitForApiResponse(
  apiCall: () => Promise<any>,
  timeout: number = 30000
): Promise<any> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      return await apiCall();
    } catch (error) {
      if (Date.now() - startTime >= timeout) {
        throw new Error(`API call timed out after ${timeout}ms: ${error}`);
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

/**
 * Verify server health
 */
export async function verifyServerHealth(): Promise<void> {
  const response = await fetch('http://localhost:3000/api/plan/health');
  expect(response.ok).toBeTruthy();
  
  const health = await response.json();
  expect(health.status).toBe('healthy');
}
