# Deployment Guide 🚀

This guide covers deployment strategies for the Browser RPA system.

## 🎯 Deployment Options

### 1. Local Development
Best for: Development and testing

### 2. PM2 Process Manager
Best for: Single-server deployments, staging environments

### 3. Cloud Platforms
Best for: Managed deployments, scalability

## 🏠 Local Development Setup

### Prerequisites
```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Install Playwright browsers
npx playwright install
```

### Setup Steps
```bash
# 1. Clone and setup
git clone <repository-url>
cd bs-BrowserRPA

# 2. Configure environment
cp .env.example .env
# Edit .env with your settings

# 3. Install dependencies
cd server && npm install && npm run build
cd ../client && npm install && npm run build

# 4. Start with PM2
pm2 start ecosystem.config.js

# 5. Monitor
pm2 status
pm2 logs
```

### PM2 Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'browser-rpa-server',
      script: 'server/dist/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: 'logs/server-error.log',
      out_file: 'logs/server-out.log',
      log_file: 'logs/server.log'
    }
  ]
};
```

## 🔧 PM2 Process Manager Deployment

### Production Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'browser-rpa-server',
      script: 'server/dist/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: 'logs/server-error.log',
      out_file: 'logs/server-out.log',
      log_file: 'logs/server.log',
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    }
  ]
};
```

### SSL Configuration with Nginx
```nginx
# nginx/nginx.conf
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /etc/ssl/certs/server.crt;
    ssl_certificate_key /etc/ssl/private/server.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Deployment Commands
```bash
# Production deployment
pm2 start ecosystem.config.js --env production

# Monitor and manage
pm2 status
pm2 logs
pm2 monit
pm2 restart browser-rpa-server
pm2 stop browser-rpa-server

# Auto-restart on system reboot
pm2 startup
pm2 save
```


## ☁️ Cloud Platform Deployment

### AWS EC2 with PM2
```bash
# Launch EC2 instance and install dependencies
sudo apt update
sudo apt install -y nodejs npm nginx

# Install PM2
npm install -g pm2

# Clone and setup application
git clone <repository-url>
cd bs-BrowserRPA
npm install

# Start with PM2
pm2 start ecosystem.config.js --env production
pm2 startup
pm2 save

# Configure Nginx as reverse proxy
sudo cp nginx/nginx.conf /etc/nginx/sites-available/browser-rpa
sudo ln -s /etc/nginx/sites-available/browser-rpa /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

### Google Cloud Compute Engine
```bash
# Create instance
gcloud compute instances create browser-rpa-server \
  --image-family=ubuntu-2004-lts \
  --image-project=ubuntu-os-cloud \
  --machine-type=e2-standard-2 \
  --tags=http-server,https-server

# SSH and setup
gcloud compute ssh browser-rpa-server

# Install Node.js and dependencies
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g pm2

# Deploy application
git clone <repository-url>
cd bs-BrowserRPA
npm install
pm2 start ecosystem.config.js --env production
```

### Azure VM Deployment
```bash
# Create VM
az vm create \
  --resource-group myResourceGroup \
  --name browser-rpa-vm \
  --image UbuntuLTS \
  --size Standard_B2s \
  --admin-username azureuser \
  --generate-ssh-keys

# Connect and setup
az vm run-command invoke \
  --resource-group myResourceGroup \
  --name browser-rpa-vm \
  --command-id RunShellScript \
  --scripts @setup-script.sh
```

## 🔧 Configuration Management

### Environment-Specific Configs
```bash
# Development
.env.development
NODE_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug

# Staging
.env.staging
NODE_ENV=staging
RATE_LIMIT_REQUESTS=20
ENABLE_DETAILED_ERRORS=true

# Production
.env.production
NODE_ENV=production
RATE_LIMIT_REQUESTS=10
ENABLE_DETAILED_ERRORS=false
```

### Configuration Validation
```javascript
// config/validator.js
const requiredEnvVars = [
  'OPENAI_API_KEY',
  'NODE_ENV',
  'PORT'
];

function validateConfig() {
  const missing = requiredEnvVars.filter(
    varName => !process.env[varName]
  );
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

module.exports = { validateConfig };
```

## 📊 Monitoring and Logging

### Prometheus Metrics
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'browser-rpa'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

### Log Aggregation
```yaml
# logging/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: browser-rpa
    environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "browser-rpa-%{+yyyy.MM.dd}"
```

## 🔐 Security Best Practices

### Application Security
```javascript
// server/src/config/security.js
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const securityMiddleware = [
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }),
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP',
  }),
];

module.exports = { securityMiddleware };
```

### Server Security
```bash
# Firewall configuration
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# SSL certificate with Let's Encrypt
sudo certbot --nginx -d yourdomain.com

# Regular security updates
sudo apt update && sudo apt upgrade -y
```

## 🚨 Disaster Recovery

### Backup Strategy
```bash
# Database backup (SQLite)
cp /path/to/database.db ./backups/database-$(date +%Y%m%d).db

# File backup
tar czf ./backups/uploads-$(date +%Y%m%d).tar.gz /app/uploads/

# Application code backup
tar czf ./backups/app-$(date +%Y%m%d).tar.gz /app/ --exclude=node_modules --exclude=uploads
```

### Restore Procedures
```bash
# Stop application
pm2 stop browser-rpa-server

# Restore database
cp ./backups/database-20231201.db /path/to/database.db

# Restore files
tar xzf ./backups/uploads-20231201.tar.gz -C /

# Restart application
pm2 start browser-rpa-server
```

## 📈 Performance Tuning

### PM2 Configuration
```javascript
// ecosystem.config.js - Optimized for performance
module.exports = {
  apps: [{
    name: 'browser-rpa-server',
    script: 'server/dist/app.js',
    instances: 'max', // Use all CPU cores
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    env: {
      NODE_ENV: 'production',
      UV_THREADPOOL_SIZE: 16 // Increase thread pool
    }
  }]
};
```

### System Optimization
```bash
# Increase file descriptor limits
echo "fs.file-max = 65536" >> /etc/sysctl.conf
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# Optimize TCP settings
echo "net.core.somaxconn = 1024" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 1024" >> /etc/sysctl.conf

# Apply settings
sysctl -p
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install dependencies and build
      run: |
        cd server && npm install && npm run build
        cd ../client && npm install && npm run build
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /app/bs-BrowserRPA
          git pull origin main
          cd server && npm install && npm run build
          cd ../client && npm install && npm run build
          pm2 restart browser-rpa-server
```

This deployment guide provides comprehensive instructions for deploying the Browser RPA system using traditional server deployment methods without containerization.