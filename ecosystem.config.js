module.exports = {
  apps: [
    {
      name: 'browser-rpa-server',
      script: 'server/dist/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // Logging
      error_file: 'logs/server-error.log',
      out_file: 'logs/server-out.log',
      log_file: 'logs/server.log',
      time: true,
      
      // Auto-restart configuration
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      max_memory_restart: '1G',
      
      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // Health monitoring
      health_check: {
        url: 'http://localhost:3000/api/plan/health',
        interval: 30000,
        timeout: 5000,
        retries: 3
      },
      
      // Auto-restart on crash
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Environment variables
      env_file: '.env',
      
      // Merge logs
      merge_logs: true,
      
      // Log rotation
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      pmx: true,
      
      // Source map support
      source_map_support: true,
      
      // Node.js options
      node_args: '--max-old-space-size=1024'
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-org/bs-BrowserRPA.git',
      path: '/var/www/browser-rpa',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt-get update && apt-get install -y git nodejs npm'
    }
  }
};