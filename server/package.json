{"name": "midscene-server", "version": "0.1.0", "main": "dist/app.js", "scripts": {"start": "ts-node src/app.ts", "build": "tsc", "db:init": "ts-node src/database/cli.ts init", "db:migrate": "ts-node src/database/cli.ts migrate", "db:reset": "ts-node src/database/cli.ts reset", "db:status": "ts-node src/database/cli.ts status", "db:seed": "ts-node src/database/cli.ts seed"}, "dependencies": {"commander": "^11.0.0", "dotenv": "16.4.5", "express": "^4.21.1", "node-fetch": "3.3.2", "socks-proxy-agent": "8.0.4", "sqlite3": "^5.1.6", "uuid": "11.1.0"}, "devDependencies": {"@types/express": "4.17.14", "@types/node": "^18.0.0", "@types/sqlite3": "^3.1.8", "ts-node": "^10.0.0", "typescript": "^5.0.0"}}