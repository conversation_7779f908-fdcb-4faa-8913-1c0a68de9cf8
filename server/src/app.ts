import 'dotenv/config';
import express from 'express';
import planRouter from './routes/plan';
import analyticsRouter from './routes/analytics';
import cleanupRouter from './routes/cleanup';
import { initializeDatabase } from './database/init';
import { logger } from './utils/logger';

const app = express();

app.use(express.json());

app.use('/api/plan', planRouter);
app.use('/api/analytics', analyticsRouter);
app.use('/api/cleanup', cleanupRouter);

const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    await initializeDatabase();
    logger.info('Database initialized successfully');
    
    app.listen(PORT, () => {
      logger.info(`MidScene Server listening on port ${PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
