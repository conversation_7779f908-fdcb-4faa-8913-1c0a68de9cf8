#!/usr/bin/env node

import { Command } from 'commander';
import { DatabaseInitializer } from './init';
import { DatabaseMigrator } from './connection';
import { logger } from '../utils/logger';

const program = new Command();
const initializer = new DatabaseInitializer();
const migrator = new DatabaseMigrator();

program
  .name('db-cli')
  .description('Database management CLI for Browser RPA')
  .version('1.0.0');

program
  .command('init')
  .description('Initialize database and run all migrations')
  .action(async () => {
    try {
      await initializer.initialize();
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database:', error);
      process.exit(1);
    }
  });

program
  .command('migrate')
  .description('Run pending migrations')
  .action(async () => {
    try {
      await migrator.runMigrations();
      logger.info('Migrations completed successfully');
    } catch (error) {
      logger.error('Failed to run migrations:', error);
      process.exit(1);
    }
  });

program
  .command('reset')
  .description('Reset database (WARNING: This will delete all data)')
  .action(async () => {
    try {
      await initializer.reset();
      logger.info('Database reset successfully');
    } catch (error) {
      logger.error('Failed to reset database:', error);
      process.exit(1);
    }
  });

program
  .command('status')
  .description('Check migration status')
  .action(async () => {
    try {
      const pendingMigrations = await migrator.getPendingMigrations();
      const appliedMigrations = await migrator.getAppliedMigrations();
      
      console.log('Applied migrations:');
      appliedMigrations.forEach(migration => {
        console.log(`  ✓ ${migration.name} (${migration.applied_at})`);
      });
      
      console.log('\nPending migrations:');
      if (pendingMigrations.length === 0) {
        console.log('  None');
      } else {
        pendingMigrations.forEach(migration => {
          console.log(`  ○ ${migration}`);
        });
      }
    } catch (error) {
      logger.error('Failed to check migration status:', error);
      process.exit(1);
    }
  });

program
  .command('seed')
  .description('Seed database with sample data')
  .action(async () => {
    try {
      const { seedDatabase } = await import('./seed');
      await seedDatabase();
      logger.info('Database seeded successfully');
    } catch (error) {
      logger.error('Failed to seed database:', error);
      process.exit(1);
    }
  });

program.parse();