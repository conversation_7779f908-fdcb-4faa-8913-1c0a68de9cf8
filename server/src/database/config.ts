/**
 * Database configuration management
 * Handles environment variables, validation, and configuration loading
 */

import { logger } from '../utils/logger';

// Database configuration interface
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
  queryTimeoutMillis: number;
}

// Environment variables mapping
const DB_ENV_VARS = {
  DB_HOST: 'Database host',
  DB_PORT: 'Database port',
  DB_NAME: 'Database name',
  DB_USER: 'Database username',
  DB_PASSWORD: 'Database password',
  DB_SSL: 'Database SSL enabled',
  DB_MAX_CONNECTIONS: 'Maximum database connections',
  DB_IDLE_TIMEOUT_MS: 'Database idle timeout',
  DB_CONNECTION_TIMEOUT_MS: 'Database connection timeout',
  DB_QUERY_TIMEOUT_MS: 'Database query timeout'
};

// Default configuration values
const DEFAULT_CONFIG: DatabaseConfig = {
  host: 'localhost',
  port: 5432,
  database: 'midscene_rpa',
  username: 'midscene',
  password: 'midscene_password',
  ssl: false,
  maxConnections: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
  queryTimeoutMillis: 30000
};

/**
 * Load database configuration from environment variables
 */
export function loadDatabaseConfig(): DatabaseConfig {
  const config: DatabaseConfig = {
    host: process.env.DB_HOST || DEFAULT_CONFIG.host,
    port: parseInt(process.env.DB_PORT || DEFAULT_CONFIG.port.toString(), 10),
    database: process.env.DB_NAME || DEFAULT_CONFIG.database,
    username: process.env.DB_USER || DEFAULT_CONFIG.username,
    password: process.env.DB_PASSWORD || DEFAULT_CONFIG.password,
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || DEFAULT_CONFIG.maxConnections.toString(), 10),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT_MS || DEFAULT_CONFIG.idleTimeoutMillis.toString(), 10),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT_MS || DEFAULT_CONFIG.connectionTimeoutMillis.toString(), 10),
    queryTimeoutMillis: parseInt(process.env.DB_QUERY_TIMEOUT_MS || DEFAULT_CONFIG.queryTimeoutMillis.toString(), 10)
  };

  // Validate configuration
  validateDatabaseConfig(config);

  return config;
}

/**
 * Validate database configuration
 */
function validateDatabaseConfig(config: DatabaseConfig): void {
  const errors: string[] = [];

  // Required fields
  if (!config.host) errors.push('Database host is required');
  if (!config.database) errors.push('Database name is required');
  if (!config.username) errors.push('Database username is required');
  if (!config.password) errors.push('Database password is required');

  // Numeric validations
  if (config.port <= 0 || config.port > 65535) {
    errors.push('Database port must be between 1 and 65535');
  }
  if (config.maxConnections <= 0) {
    errors.push('Maximum connections must be greater than 0');
  }
  if (config.idleTimeoutMillis < 0) {
    errors.push('Idle timeout must be non-negative');
  }
  if (config.connectionTimeoutMillis < 0) {
    errors.push('Connection timeout must be non-negative');
  }
  if (config.queryTimeoutMillis < 0) {
    errors.push('Query timeout must be non-negative');
  }

  if (errors.length > 0) {
    logger.error('Database configuration validation failed', { errors });
    throw new Error(`Database configuration validation failed: ${errors.join(', ')}`);
  }
}

/**
 * Get database configuration without sensitive information
 */
export function getSafeConfig(config: DatabaseConfig): Omit<DatabaseConfig, 'password'> {
  const { password, ...safeConfig } = config;
  return safeConfig;
}

/**
 * Generate database connection string
 */
export function generateConnectionString(config: DatabaseConfig): string {
  const sslParam = config.ssl ? '?ssl=true' : '';
  return `postgresql://${config.username}:${config.password}@${config.host}:${config.port}/${config.database}${sslParam}`;
}

/**
 * Generate database connection string without password (for logging)
 */
export function generateSafeConnectionString(config: DatabaseConfig): string {
  const sslParam = config.ssl ? '?ssl=true' : '';
  return `postgresql://${config.username}:****@${config.host}:${config.port}/${config.database}${sslParam}`;
}

/**
 * Check if all required environment variables are present
 */
export function checkRequiredEnvVars(): {
  missing: string[];
  present: string[];
  warnings: string[];
} {
  const missing: string[] = [];
  const present: string[] = [];
  const warnings: string[] = [];

  // Check required environment variables
  const requiredVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
  
  for (const envVar of requiredVars) {
    if (process.env[envVar]) {
      present.push(envVar);
    } else {
      missing.push(envVar);
    }
  }

  // Check optional but recommended variables
  const recommendedVars = ['DB_PORT', 'DB_SSL', 'DB_MAX_CONNECTIONS'];
  
  for (const envVar of recommendedVars) {
    if (!process.env[envVar]) {
      warnings.push(`${envVar} not set, using default value`);
    }
  }

  return { missing, present, warnings };
}

/**
 * Get environment variable descriptions
 */
export function getEnvVarDescriptions(): Record<string, string> {
  return DB_ENV_VARS;
}

/**
 * Generate example .env file content
 */
export function generateExampleEnvFile(): string {
  const envVars = [
    '# Database Configuration',
    '# PostgreSQL database connection settings',
    '',
    'DB_HOST=localhost',
    'DB_PORT=5432',
    'DB_NAME=midscene_rpa',
    'DB_USER=midscene',
    'DB_PASSWORD=midscene_password',
    'DB_SSL=false',
    '',
    '# Database Pool Settings',
    'DB_MAX_CONNECTIONS=10',
    'DB_IDLE_TIMEOUT_MS=30000',
    'DB_CONNECTION_TIMEOUT_MS=10000',
    'DB_QUERY_TIMEOUT_MS=30000',
    '',
    '# Production Example (uncomment and modify):',
    '# DB_HOST=prod-db.example.com',
    '# DB_PORT=5432',
    '# DB_NAME=midscene_production',
    '# DB_USER=midscene_prod',
    '# DB_PASSWORD=your_secure_password_here',
    '# DB_SSL=true',
    '# DB_MAX_CONNECTIONS=20',
    ''
  ];

  return envVars.join('\n');
}

/**
 * Database configuration validation and setup helper
 */
export class DatabaseConfigManager {
  private static instance: DatabaseConfigManager;
  private config: DatabaseConfig | null = null;

  private constructor() {}

  public static getInstance(): DatabaseConfigManager {
    if (!DatabaseConfigManager.instance) {
      DatabaseConfigManager.instance = new DatabaseConfigManager();
    }
    return DatabaseConfigManager.instance;
  }

  /**
   * Initialize and validate configuration
   */
  public async initialize(): Promise<DatabaseConfig> {
    if (this.config) {
      return this.config;
    }

    logger.info('Initializing database configuration');

    // Check environment variables
    const envCheck = checkRequiredEnvVars();
    
    if (envCheck.missing.length > 0) {
      logger.error('Missing required environment variables', {
        missing: envCheck.missing,
        present: envCheck.present
      });
      throw new Error(`Missing required environment variables: ${envCheck.missing.join(', ')}`);
    }

    if (envCheck.warnings.length > 0) {
      logger.warn('Using default values for optional environment variables', {
        warnings: envCheck.warnings
      });
    }

    // Load and validate configuration
    this.config = loadDatabaseConfig();
    
    logger.info('Database configuration loaded successfully', {
      config: getSafeConfig(this.config),
      connectionString: generateSafeConnectionString(this.config)
    });

    return this.config;
  }

  /**
   * Get current configuration
   */
  public getConfig(): DatabaseConfig {
    if (!this.config) {
      throw new Error('Database configuration not initialized. Call initialize() first.');
    }
    return this.config;
  }

  /**
   * Update configuration (for testing or runtime changes)
   */
  public updateConfig(updates: Partial<DatabaseConfig>): void {
    if (!this.config) {
      throw new Error('Database configuration not initialized. Call initialize() first.');
    }

    this.config = { ...this.config, ...updates };
    validateDatabaseConfig(this.config);
    
    logger.info('Database configuration updated', {
      updates: Object.keys(updates),
      config: getSafeConfig(this.config)
    });
  }

  /**
   * Reset configuration (for testing)
   */
  public reset(): void {
    this.config = null;
    logger.debug('Database configuration reset');
  }
}