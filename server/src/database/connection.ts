import { Pool, PoolClient, PoolConfig } from 'pg';
import { logger } from '../utils/logger';

// Database configuration interface
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
  queryTimeoutMillis: number;
}

// Database connection pool
let pool: Pool | null = null;

/**
 * Load database configuration from environment variables
 */
function loadDatabaseConfig(): DatabaseConfig {
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'midscene_rpa',
    username: process.env.DB_USER || 'midscene',
    password: process.env.DB_PASSWORD || 'midscene_password',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10', 10),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT_MS || '30000', 10),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT_MS || '10000', 10),
    queryTimeoutMillis: parseInt(process.env.DB_QUERY_TIMEOUT_MS || '30000', 10)
  };
}

/**
 * Initialize database connection pool
 */
export async function initializeDatabase(): Promise<void> {
  if (pool) {
    logger.warn('Database pool already initialized');
    return;
  }

  const config = loadDatabaseConfig();
  
  const poolConfig: PoolConfig = {
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.username,
    password: config.password,
    ssl: config.ssl,
    max: config.maxConnections,
    idleTimeoutMillis: config.idleTimeoutMillis,
    connectionTimeoutMillis: config.connectionTimeoutMillis,
    statement_timeout: config.queryTimeoutMillis
  };

  pool = new Pool(poolConfig);

  // Test the connection
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();
    
    logger.info('Database connection established successfully', {
      host: config.host,
      port: config.port,
      database: config.database,
      timestamp: result.rows[0].now
    });
  } catch (error) {
    logger.error('Failed to establish database connection', {
      error: error instanceof Error ? error.message : 'Unknown error',
      config: {
        host: config.host,
        port: config.port,
        database: config.database,
        username: config.username
      }
    });
    throw error;
  }

  // Handle pool errors
  pool.on('error', (err) => {
    logger.error('Database pool error', { error: err.message });
  });

  pool.on('connect', () => {
    logger.debug('New database connection established');
  });

  pool.on('acquire', () => {
    logger.debug('Database connection acquired from pool');
  });

  pool.on('release', () => {
    logger.debug('Database connection released back to pool');
  });
}

/**
 * Get database connection pool
 */
export function getPool(): Pool {
  if (!pool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pool;
}

/**
 * Get a database client from the pool
 */
export async function getClient(): Promise<PoolClient> {
  const dbPool = getPool();
  return await dbPool.connect();
}

/**
 * Execute a query with automatic connection management
 */
export async function query(text: string, params?: any[]): Promise<any> {
  const client = await getClient();
  try {
    const start = Date.now();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Query executed', {
      query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      duration,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('Query execution failed', {
      query: text,
      params,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Execute a transaction with automatic rollback on error
 */
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction rolled back', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Close database connection pool
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    logger.info('Database connection pool closed');
  }
}

/**
 * Check database health
 */
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  totalConnections: number;
  idleConnections: number;
  waitingConnections: number;
  responseTime: number;
}> {
  if (!pool) {
    return {
      healthy: false,
      totalConnections: 0,
      idleConnections: 0,
      waitingConnections: 0,
      responseTime: 0
    };
  }

  const start = Date.now();
  
  try {
    await query('SELECT 1');
    const responseTime = Date.now() - start;
    
    return {
      healthy: true,
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingConnections: pool.waitingCount,
      responseTime
    };
  } catch (error) {
    logger.error('Database health check failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    return {
      healthy: false,
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingConnections: pool.waitingCount,
      responseTime: Date.now() - start
    };
  }
}

/**
 * Get database configuration (without sensitive data)
 */
export function getDatabaseConfig(): Omit<DatabaseConfig, 'password'> {
  const config = loadDatabaseConfig();
  const { password, ...safeConfig } = config;
  return safeConfig;
}

/**
 * Database migration utilities
 */
export class DatabaseMigrator {
  private static async tableExists(tableName: string): Promise<boolean> {
    const result = await query(
      'SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)',
      [tableName]
    );
    return result.rows[0].exists;
  }

  private static async createMigrationsTable(): Promise<void> {
    await query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
  }

  private static async isMigrationExecuted(migrationName: string): Promise<boolean> {
    const result = await query(
      'SELECT EXISTS (SELECT 1 FROM migrations WHERE name = $1)',
      [migrationName]
    );
    return result.rows[0].exists;
  }

  private static async recordMigration(migrationName: string): Promise<void> {
    await query(
      'INSERT INTO migrations (name) VALUES ($1)',
      [migrationName]
    );
  }

  public static async runMigration(
    migrationName: string,
    migrationSql: string
  ): Promise<void> {
    await this.createMigrationsTable();
    
    if (await this.isMigrationExecuted(migrationName)) {
      logger.info(`Migration ${migrationName} already executed, skipping`);
      return;
    }

    logger.info(`Running migration: ${migrationName}`);
    
    await transaction(async (client) => {
      await client.query(migrationSql);
      await client.query(
        'INSERT INTO migrations (name) VALUES ($1)',
        [migrationName]
      );
    });

    logger.info(`Migration ${migrationName} completed successfully`);
  }

  public static async getMigrationStatus(): Promise<{
    executedMigrations: string[];
    pendingMigrations: string[];
  }> {
    await this.createMigrationsTable();
    
    const result = await query(
      'SELECT name FROM migrations ORDER BY executed_at'
    );
    
    return {
      executedMigrations: result.rows.map((row: any) => row.name),
      pendingMigrations: [] // TODO: Implement pending migrations detection
    };
  }
}