import { DatabaseMigrator } from './connection';
import { logger } from '../utils/logger';

export class DatabaseInitializer {
  private migrator: DatabaseMigrator;

  constructor() {
    this.migrator = new DatabaseMigrator();
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing database...');
      
      await this.migrator.runMigrations();
      
      logger.info('Database initialization completed successfully');
    } catch (error) {
      logger.error('Database initialization failed:', error);
      throw error;
    }
  }

  async reset(): Promise<void> {
    try {
      logger.info('Resetting database...');
      
      await this.migrator.reset();
      await this.migrator.runMigrations();
      
      logger.info('Database reset completed successfully');
    } catch (error) {
      logger.error('Database reset failed:', error);
      throw error;
    }
  }
}

export const initializeDatabase = async (): Promise<void> => {
  const initializer = new DatabaseInitializer();
  await initializer.initialize();
};