/**
 * Performance optimization migration
 * Adds additional indexes for better query performance
 */

import { DatabaseMigrator } from '../connection';

const MIGRATION_NAME = '002_add_indexes';

const MIGRATION_SQL = `
-- Additional performance indexes

-- Session performance indexes
CREATE INDEX IF NOT EXISTS idx_sessions_user_id_created_at ON sessions(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_sessions_is_active_updated_at ON sessions(is_active, updated_at);

-- Plan performance indexes
CREATE INDEX IF NOT EXISTS idx_plans_user_instruction_trgm ON plans USING gin(user_instruction gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_plans_session_created_at ON plans(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_plans_provider_model ON plans(provider_used, model_used);
CREATE INDEX IF NOT EXISTS idx_plans_cost_created_at ON plans(cost_usd, created_at);

-- Execution performance indexes
CREATE INDEX IF NOT EXISTS idx_executions_status_created_at ON executions(overall_status, created_at);
CREATE INDEX IF NOT EXISTS idx_executions_plan_created_at ON executions(plan_id, created_at);
CREATE INDEX IF NOT EXISTS idx_executions_execution_time ON executions(total_execution_time_ms);

-- Execution steps performance indexes
CREATE INDEX IF NOT EXISTS idx_execution_steps_execution_step ON execution_steps(execution_id, step_number);
CREATE INDEX IF NOT EXISTS idx_execution_steps_action_type_status ON execution_steps(action_type, status);
CREATE INDEX IF NOT EXISTS idx_execution_steps_status_created_at ON execution_steps(status, created_at);

-- Provider metrics performance indexes
CREATE INDEX IF NOT EXISTS idx_provider_metrics_provider_created_at ON provider_metrics(provider_name, created_at);
CREATE INDEX IF NOT EXISTS idx_provider_metrics_success_created_at ON provider_metrics(success, created_at);
CREATE INDEX IF NOT EXISTS idx_provider_metrics_cost_created_at ON provider_metrics(cost_usd, created_at);

-- System health performance indexes
CREATE INDEX IF NOT EXISTS idx_system_health_period_interval ON system_health(period_start, interval_type);

-- DOM uploads performance indexes
CREATE INDEX IF NOT EXISTS idx_dom_uploads_session_created_at ON dom_uploads(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_dom_uploads_file_size ON dom_uploads(file_size);

-- User analytics performance indexes
CREATE INDEX IF NOT EXISTS idx_user_analytics_user_last_seen ON user_analytics(user_id, last_seen);
CREATE INDEX IF NOT EXISTS idx_user_analytics_session_last_seen ON user_analytics(session_id, last_seen);

-- Configuration performance indexes
CREATE INDEX IF NOT EXISTS idx_configuration_category_key ON configuration(category, config_key);

-- Enable trigram extension for text search (if not already enabled)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Add full-text search index for plan instructions
CREATE INDEX IF NOT EXISTS idx_plans_instruction_fts ON plans USING gin(to_tsvector('english', user_instruction));

-- Add composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_plans_session_status_created ON plans(session_id, validation_passed, created_at);
CREATE INDEX IF NOT EXISTS idx_executions_session_status_created ON executions(session_id, overall_status, created_at);

-- Add partial indexes for active/recent data
CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(created_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_recent_executions ON executions(created_at) WHERE created_at >= NOW() - INTERVAL '7 days';
CREATE INDEX IF NOT EXISTS idx_recent_plans ON plans(created_at) WHERE created_at >= NOW() - INTERVAL '7 days';
`;

/**
 * Run the indexes migration
 */
export async function runIndexesMigration(): Promise<void> {
  await DatabaseMigrator.runMigration(MIGRATION_NAME, MIGRATION_SQL);
}

/**
 * Migration metadata
 */
export const migration = {
  name: MIGRATION_NAME,
  description: 'Add performance indexes and full-text search capabilities',
  version: '1.1.0',
  run: runIndexesMigration
};