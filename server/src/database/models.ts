/**
 * Database models and interfaces for persistence layer
 * Maps to the database schema defined in schema.sql
 */

// Base interface for all database entities
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at?: Date;
}

// Session tracking interface
export interface Session extends BaseEntity {
  session_id: string;
  user_id?: string;
  screen_width?: number;
  screen_height?: number;
  user_agent?: string;
  is_active: boolean;
}

// Session creation request
export interface CreateSessionRequest {
  session_id: string;
  user_id?: string;
  screen_width?: number;
  screen_height?: number;
  user_agent?: string;
}

// DOM upload tracking interface
export interface DomUpload extends BaseEntity {
  dom_id: string;
  session_id: string;
  file_path: string;
  file_size?: number;
  node_count?: number;
  form_count: number;
  button_count: number;
  input_count: number;
  link_count: number;
  image_count: number;
}

// DOM upload creation request
export interface CreateDomUploadRequest {
  dom_id: string;
  session_id: string;
  file_path: string;
  file_size?: number;
  node_count?: number;
  form_count?: number;
  button_count?: number;
  input_count?: number;
  link_count?: number;
  image_count?: number;
}

// Plan storage interface
export interface Plan extends BaseEntity {
  plan_id: string;
  session_id: string;
  dom_upload_id?: string;
  user_instruction: string;
  action_summary?: string;
  actions_json: any; // JSON representation of PlanningAction[]
  action_count?: number;
  provider_used?: string;
  model_used?: string;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_usd?: number;
  generation_time_ms?: number;
  retry_count: number;
  validation_passed?: boolean;
  fallback_used: boolean;
  screenshot_url?: string;
  screenshot_file_path?: string;
}

// Plan creation request
export interface CreatePlanRequest {
  plan_id: string;
  session_id: string;
  dom_upload_id?: string;
  user_instruction: string;
  action_summary?: string;
  actions_json: any;
  action_count?: number;
  provider_used?: string;
  model_used?: string;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_usd?: number;
  generation_time_ms?: number;
  retry_count?: number;
  validation_passed?: boolean;
  fallback_used?: boolean;
  screenshot_url?: string;
  screenshot_file_path?: string;
}

// Execution tracking interface
export interface Execution extends BaseEntity {
  execution_id: string;
  plan_id: string;
  session_id: string;
  overall_status: 'success' | 'failed' | 'partial';
  total_steps?: number;
  successful_steps?: number;
  failed_steps?: number;
  error_message?: string;
  failed_step_id?: string;
  failed_step_number?: number;
  execution_start_time?: Date;
  execution_end_time?: Date;
  total_execution_time_ms?: number;
  result_file_path?: string;
}

// Execution creation request
export interface CreateExecutionRequest {
  execution_id: string;
  plan_id: string;
  session_id: string;
  overall_status: 'success' | 'failed' | 'partial';
  total_steps?: number;
  successful_steps?: number;
  failed_steps?: number;
  error_message?: string;
  failed_step_id?: string;
  failed_step_number?: number;
  execution_start_time?: Date;
  execution_end_time?: Date;
  total_execution_time_ms?: number;
  result_file_path?: string;
}

// Execution step interface
export interface ExecutionStep extends BaseEntity {
  execution_id: string;
  plan_id: string;
  step_number: number;
  action_id: string;
  action_type?: string;
  status: 'success' | 'failed' | 'skipped';
  log_message?: string;
  error_message?: string;
  target_element_xpath?: string;
  target_element_bbox?: any; // JSON representation of bbox
  action_parameter?: string;
  thought?: string;
  screenshot_base64?: string;
  screenshot_file_path?: string;
  step_start_time?: Date;
  step_end_time?: Date;
  execution_time_ms?: number;
  retry_count: number;
}

// Execution step creation request
export interface CreateExecutionStepRequest {
  execution_id: string;
  plan_id: string;
  step_number: number;
  action_id: string;
  action_type?: string;
  status: 'success' | 'failed' | 'skipped';
  log_message?: string;
  error_message?: string;
  target_element_xpath?: string;
  target_element_bbox?: any;
  action_parameter?: string;
  thought?: string;
  screenshot_base64?: string;
  screenshot_file_path?: string;
  step_start_time?: Date;
  step_end_time?: Date;
  execution_time_ms?: number;
  retry_count?: number;
}

// Provider metrics interface
export interface ProviderMetric extends BaseEntity {
  provider_name: string;
  model_name: string;
  request_type?: string;
  plan_id?: string;
  response_time_ms?: number;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_usd?: number;
  success: boolean;
  error_type?: string;
  error_message?: string;
  provider_health_score?: number;
  circuit_breaker_triggered: boolean;
}

// Provider metrics creation request
export interface CreateProviderMetricRequest {
  provider_name: string;
  model_name: string;
  request_type?: string;
  plan_id?: string;
  response_time_ms?: number;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_usd?: number;
  success: boolean;
  error_type?: string;
  error_message?: string;
  provider_health_score?: number;
  circuit_breaker_triggered?: boolean;
}

// System health tracking interface
export interface SystemHealth extends BaseEntity {
  period_start: Date;
  period_end: Date;
  interval_type: 'minute' | 'hour' | 'day' | 'week' | 'month';
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  avg_response_time_ms?: number;
  avg_execution_time_ms?: number;
  avg_plan_generation_time_ms?: number;
  total_cost_usd?: number;
  total_tokens_used?: number;
  error_rate?: number;
  top_error_types?: any; // JSON
  provider_usage_stats?: any; // JSON
}

// User analytics interface
export interface UserAnalytics extends BaseEntity {
  user_id?: string;
  session_id: string;
  total_sessions: number;
  total_plans_generated: number;
  total_executions: number;
  total_successful_executions: number;
  avg_instruction_length?: number;
  common_action_types?: any; // JSON
  preferred_providers?: any; // JSON
  most_active_hours?: any; // JSON
  session_duration_avg_ms?: number;
  success_rate?: number;
  common_failure_types?: any; // JSON
  first_seen?: Date;
  last_seen?: Date;
}

// Configuration interface
export interface Configuration extends BaseEntity {
  config_key: string;
  config_value?: string;
  config_type?: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  category?: string;
  is_sensitive: boolean;
  validation_rule?: string;
  default_value?: string;
  updated_by?: string;
}

// Data retention policy interface
export interface DataRetentionPolicy extends BaseEntity {
  table_name: string;
  retention_days: number;
  archive_before_delete: boolean;
  cleanup_condition?: string;
  archive_location?: string;
  is_active: boolean;
  last_cleanup_run?: Date;
  next_cleanup_run?: Date;
}

// Analytics summary interfaces
export interface DailyExecutionSummary {
  date: string;
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  avg_execution_time_ms: number;
  avg_success_rate: number;
}

export interface ProviderPerformanceSummary {
  provider_name: string;
  model_name: string;
  total_requests: number;
  successful_requests: number;
  avg_response_time_ms: number;
  avg_cost_usd: number;
  total_tokens_used: number;
  avg_health_score: number;
}

export interface SessionActivitySummary {
  session_id: string;
  user_id?: string;
  session_start: Date;
  plans_generated: number;
  executions_attempted: number;
  successful_executions: number;
  last_activity: Date;
}

// Query filter interfaces
export interface PaginationOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export interface DateRangeFilter {
  start_date?: Date;
  end_date?: Date;
}

export interface SessionFilter extends PaginationOptions, DateRangeFilter {
  user_id?: string;
  is_active?: boolean;
}

export interface PlanFilter extends PaginationOptions, DateRangeFilter {
  session_id?: string;
  provider_used?: string;
  model_used?: string;
  validation_passed?: boolean;
  fallback_used?: boolean;
}

export interface ExecutionFilter extends PaginationOptions, DateRangeFilter {
  session_id?: string;
  plan_id?: string;
  overall_status?: 'success' | 'failed' | 'partial';
}

export interface ProviderMetricFilter extends PaginationOptions, DateRangeFilter {
  provider_name?: string;
  model_name?: string;
  success?: boolean;
  request_type?: string;
}

// Complex query result interfaces
export interface ExecutionWithSteps extends Execution {
  steps: ExecutionStep[];
  plan?: Plan;
  session?: Session;
}

export interface PlanWithExecution extends Plan {
  execution?: Execution;
  session?: Session;
  dom_upload?: DomUpload;
}

export interface SessionWithActivity extends Session {
  plans_count: number;
  executions_count: number;
  last_activity: Date;
  success_rate: number;
}

// Database operation result interfaces
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  affected_rows?: number;
}

export interface QueryResult<T = any> {
  rows: T[];
  count?: number;
  total_count?: number;
}

// Bulk operation interfaces
export interface BulkCreateRequest<T> {
  records: T[];
  ignore_conflicts?: boolean;
  return_conflicts?: boolean;
}

export interface BulkUpdateRequest<T> {
  records: Partial<T>[];
  where_clause: string;
  where_params?: any[];
}

export interface BulkDeleteRequest {
  where_clause: string;
  where_params?: any[];
  soft_delete?: boolean;
}

// Migration interface
export interface Migration {
  id: number;
  name: string;
  executed_at: Date;
}

// Database health interface
export interface DatabaseHealth {
  healthy: boolean;
  total_connections: number;
  idle_connections: number;
  waiting_connections: number;
  response_time: number;
}

// Export all models in a namespace for easier imports
export namespace Models {
  export type Session = Session;
  export type DomUpload = DomUpload;
  export type Plan = Plan;
  export type Execution = Execution;
  export type ExecutionStep = ExecutionStep;
  export type ProviderMetric = ProviderMetric;
  export type SystemHealth = SystemHealth;
  export type UserAnalytics = UserAnalytics;
  export type Configuration = Configuration;
  export type DataRetentionPolicy = DataRetentionPolicy;
}