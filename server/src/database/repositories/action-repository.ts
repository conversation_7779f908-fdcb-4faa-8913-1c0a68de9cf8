import { Database } from 'sqlite3';
import { database } from '../connection';
import { logger } from '../../utils/logger';

export interface Action {
  id: string;
  execution_id: string;
  type: string;
  selector?: string;
  value?: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: Date;
  duration?: number;
  error_message?: string;
  screenshot_path?: string;
  metadata?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateActionRequest {
  execution_id: string;
  type: string;
  selector?: string;
  value?: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: Date;
  duration?: number;
  error_message?: string;
  screenshot_path?: string;
  metadata?: string;
}

export class ActionRepository {
  private db: Database;

  constructor() {
    this.db = database;
  }

  async create(data: CreateActionRequest): Promise<Action> {
    return new Promise((resolve, reject) => {
      const id = require('uuid').v4();
      const now = new Date();
      
      const query = `
        INSERT INTO actions (
          id, execution_id, type, selector, value, status, 
          timestamp, duration, error_message, screenshot_path, 
          metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      this.db.run(query, [
        id,
        data.execution_id,
        data.type,
        data.selector,
        data.value,
        data.status,
        data.timestamp.toISOString(),
        data.duration,
        data.error_message,
        data.screenshot_path,
        data.metadata,
        now.toISOString(),
        now.toISOString()
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            ...data,
            created_at: now,
            updated_at: now
          });
        }
      });
    });
  }

  async findById(id: string): Promise<Action | null> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM actions WHERE id = ?';
      
      this.db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row ? this.mapRowToAction(row) : null);
        }
      });
    });
  }

  async findByExecutionId(executionId: string): Promise<Action[]> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM actions WHERE execution_id = ? ORDER BY timestamp ASC';
      
      this.db.all(query, [executionId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToAction(row)));
        }
      });
    });
  }

  async findAll(): Promise<Action[]> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM actions ORDER BY timestamp DESC';
      
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToAction(row)));
        }
      });
    });
  }

  async count(): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT COUNT(*) as count FROM actions';
      
      this.db.get(query, [], (err, row: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count);
        }
      });
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Action[]> {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM actions 
        WHERE timestamp BETWEEN ? AND ?
        ORDER BY timestamp DESC
      `;
      
      this.db.all(query, [startDate.toISOString(), endDate.toISOString()], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToAction(row)));
        }
      });
    });
  }

  async deleteByExecutionId(executionId: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM actions WHERE execution_id = ?';
      
      this.db.run(query, [executionId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async deleteOldest(count: number): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = `
        DELETE FROM actions 
        WHERE id IN (
          SELECT id FROM actions 
          ORDER BY timestamp ASC 
          LIMIT ?
        )
      `;
      
      this.db.run(query, [count], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async update(id: string, data: Partial<CreateActionRequest>): Promise<void> {
    return new Promise((resolve, reject) => {
      const fields = [];
      const values = [];
      
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }
      
      if (fields.length === 0) {
        resolve();
        return;
      }
      
      fields.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(id);
      
      const query = `UPDATE actions SET ${fields.join(', ')} WHERE id = ?`;
      
      this.db.run(query, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async delete(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM actions WHERE id = ?';
      
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  private mapRowToAction(row: any): Action {
    return {
      id: row.id,
      execution_id: row.execution_id,
      type: row.type,
      selector: row.selector,
      value: row.value,
      status: row.status,
      timestamp: new Date(row.timestamp),
      duration: row.duration,
      error_message: row.error_message,
      screenshot_path: row.screenshot_path,
      metadata: row.metadata,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }
}