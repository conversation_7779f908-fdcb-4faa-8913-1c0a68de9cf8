/**
 * Base repository class with common database operations
 * Provides a foundation for all entity repositories
 */

import { PoolClient } from 'pg';
import { query, transaction, getClient } from '../connection';
import { 
  BaseEntity, 
  OperationResult, 
  QueryResult, 
  PaginationOptions,
  BulkCreateRequest,
  BulkUpdateRequest,
  BulkDeleteRequest
} from '../models';
import { logger } from '../../utils/logger';

export abstract class BaseRepository<T extends BaseEntity, CreateT = Partial<T>> {
  protected abstract readonly tableName: string;
  protected abstract readonly primaryKey: string;

  /**
   * Build WHERE clause from filter object
   */
  protected buildWhereClause(filter: Record<string, any>): { clause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(filter)) {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          conditions.push(`${key} = ANY($${paramIndex})`);
          params.push(value);
        } else {
          conditions.push(`${key} = $${paramIndex}`);
          params.push(value);
        }
        paramIndex++;
      }
    }

    return {
      clause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
      params
    };
  }

  /**
   * Build ORDER BY clause from pagination options
   */
  protected buildOrderByClause(options: PaginationOptions): string {
    const { orderBy = 'created_at', orderDirection = 'DESC' } = options;
    return `ORDER BY ${orderBy} ${orderDirection}`;
  }

  /**
   * Build LIMIT and OFFSET clause
   */
  protected buildLimitClause(options: PaginationOptions): { clause: string; params: any[] } {
    const { limit, offset } = options;
    const clauses: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (limit) {
      clauses.push(`LIMIT $${paramIndex}`);
      params.push(limit);
      paramIndex++;
    }

    if (offset) {
      clauses.push(`OFFSET $${paramIndex}`);
      params.push(offset);
      paramIndex++;
    }

    return {
      clause: clauses.join(' '),
      params
    };
  }

  /**
   * Find single record by ID
   */
  public async findById(id: string): Promise<T | null> {
    try {
      const result = await query(
        `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = $1`,
        [id]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error(`Error finding ${this.tableName} by ID`, {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find all records with optional filtering and pagination
   */
  public async findAll(
    filter: Partial<T> = {},
    options: PaginationOptions = {}
  ): Promise<QueryResult<T>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(options);
      const limitClause = this.buildLimitClause(options);

      const queryText = `
        SELECT * FROM ${this.tableName}
        ${whereClause.clause}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await query(queryText, params);

      // Get total count for pagination
      let totalCount = 0;
      if (options.limit || options.offset) {
        const countResult = await query(
          `SELECT COUNT(*) FROM ${this.tableName} ${whereClause.clause}`,
          whereClause.params
        );
        totalCount = parseInt(countResult.rows[0].count, 10);
      }

      return {
        rows: result.rows,
        count: result.rows.length,
        total_count: totalCount || result.rows.length
      };
    } catch (error) {
      logger.error(`Error finding all ${this.tableName}`, {
        filter,
        options,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Create new record
   */
  public async create(data: CreateT): Promise<OperationResult<T>> {
    try {
      const keys = Object.keys(data as any);
      const values = Object.values(data as any);
      const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
      const columns = keys.join(', ');

      const queryText = `
        INSERT INTO ${this.tableName} (${columns})
        VALUES (${placeholders})
        RETURNING *
      `;

      const result = await query(queryText, values);

      return {
        success: true,
        data: result.rows[0],
        affected_rows: result.rowCount
      };
    } catch (error) {
      logger.error(`Error creating ${this.tableName}`, {
        data,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update record by ID
   */
  public async update(id: string, data: Partial<T>): Promise<OperationResult<T>> {
    try {
      const keys = Object.keys(data as any);
      const values = Object.values(data as any);
      const setClause = keys.map((key, index) => `${key} = $${index + 2}`).join(', ');

      const queryText = `
        UPDATE ${this.tableName}
        SET ${setClause}, updated_at = NOW()
        WHERE ${this.primaryKey} = $1
        RETURNING *
      `;

      const result = await query(queryText, [id, ...values]);

      if (result.rows.length === 0) {
        return {
          success: false,
          error: 'Record not found'
        };
      }

      return {
        success: true,
        data: result.rows[0],
        affected_rows: result.rowCount
      };
    } catch (error) {
      logger.error(`Error updating ${this.tableName}`, {
        id,
        data,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Delete record by ID
   */
  public async delete(id: string): Promise<OperationResult<void>> {
    try {
      const result = await query(
        `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = $1`,
        [id]
      );

      if (result.rowCount === 0) {
        return {
          success: false,
          error: 'Record not found'
        };
      }

      return {
        success: true,
        affected_rows: result.rowCount
      };
    } catch (error) {
      logger.error(`Error deleting ${this.tableName}`, {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Bulk create records
   */
  public async bulkCreate(request: BulkCreateRequest<CreateT>): Promise<OperationResult<T[]>> {
    if (request.records.length === 0) {
      return {
        success: true,
        data: [],
        affected_rows: 0
      };
    }

    try {
      const result = await transaction(async (client) => {
        const insertedRecords: T[] = [];

        for (const record of request.records) {
          const keys = Object.keys(record as any);
          const values = Object.values(record as any);
          const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
          const columns = keys.join(', ');

          const queryText = `
            INSERT INTO ${this.tableName} (${columns})
            VALUES (${placeholders})
            ${request.ignore_conflicts ? 'ON CONFLICT DO NOTHING' : ''}
            RETURNING *
          `;

          const result = await client.query(queryText, values);
          if (result.rows.length > 0) {
            insertedRecords.push(result.rows[0]);
          }
        }

        return insertedRecords;
      });

      return {
        success: true,
        data: result,
        affected_rows: result.length
      };
    } catch (error) {
      logger.error(`Error bulk creating ${this.tableName}`, {
        recordCount: request.records.length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Bulk update records
   */
  public async bulkUpdate(request: BulkUpdateRequest<T>): Promise<OperationResult<number>> {
    try {
      const result = await transaction(async (client) => {
        let totalUpdated = 0;

        for (const record of request.records) {
          const keys = Object.keys(record as any);
          const values = Object.values(record as any);
          const setClause = keys.map((key, index) => `${key} = $${index + 1}`).join(', ');

          const queryText = `
            UPDATE ${this.tableName}
            SET ${setClause}, updated_at = NOW()
            ${request.where_clause}
          `;

          const result = await client.query(queryText, [...values, ...(request.where_params || [])]);
          totalUpdated += result.rowCount || 0;
        }

        return totalUpdated;
      });

      return {
        success: true,
        data: result,
        affected_rows: result
      };
    } catch (error) {
      logger.error(`Error bulk updating ${this.tableName}`, {
        recordCount: request.records.length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Bulk delete records
   */
  public async bulkDelete(request: BulkDeleteRequest): Promise<OperationResult<number>> {
    try {
      const queryText = `DELETE FROM ${this.tableName} ${request.where_clause}`;
      const result = await query(queryText, request.where_params || []);

      return {
        success: true,
        data: result.rowCount || 0,
        affected_rows: result.rowCount || 0
      };
    } catch (error) {
      logger.error(`Error bulk deleting ${this.tableName}`, {
        whereClause: request.where_clause,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if record exists
   */
  public async exists(id: string): Promise<boolean> {
    try {
      const result = await query(
        `SELECT 1 FROM ${this.tableName} WHERE ${this.primaryKey} = $1 LIMIT 1`,
        [id]
      );

      return result.rows.length > 0;
    } catch (error) {
      logger.error(`Error checking existence in ${this.tableName}`, {
        id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Get record count with optional filter
   */
  public async count(filter: Partial<T> = {}): Promise<number> {
    try {
      const whereClause = this.buildWhereClause(filter);
      
      const result = await query(
        `SELECT COUNT(*) FROM ${this.tableName} ${whereClause.clause}`,
        whereClause.params
      );

      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      logger.error(`Error counting ${this.tableName}`, {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Execute custom query with this repository's context
   */
  protected async customQuery(queryText: string, params: any[] = []): Promise<QueryResult<any>> {
    try {
      const result = await query(queryText, params);
      return {
        rows: result.rows,
        count: result.rows.length
      };
    } catch (error) {
      logger.error(`Error executing custom query in ${this.tableName}`, {
        query: queryText.substring(0, 100) + (queryText.length > 100 ? '...' : ''),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}