import { Database } from 'sqlite3';
import { database } from '../connection';
import { logger } from '../../utils/logger';

export interface Execution {
  id: string;
  plan_id: string;
  status: string;
  start_time: Date;
  end_time?: Date;
  duration?: number;
  success_rate: number;
  error_count: number;
  metadata?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateExecutionRequest {
  plan_id: string;
  status: string;
  start_time: Date;
  end_time?: Date;
  duration?: number;
  success_rate: number;
  error_count: number;
  metadata?: string;
}

export class ExecutionRepository {
  private db: Database;

  constructor() {
    this.db = database;
  }

  async create(data: CreateExecutionRequest): Promise<Execution> {
    return new Promise((resolve, reject) => {
      const id = require('uuid').v4();
      const now = new Date();
      
      const query = `
        INSERT INTO executions (
          id, plan_id, status, start_time, end_time, duration, 
          success_rate, error_count, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      this.db.run(query, [
        id,
        data.plan_id,
        data.status,
        data.start_time.toISOString(),
        data.end_time?.toISOString(),
        data.duration,
        data.success_rate,
        data.error_count,
        data.metadata,
        now.toISOString(),
        now.toISOString()
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            ...data,
            created_at: now,
            updated_at: now
          });
        }
      });
    });
  }

  async findById(id: string): Promise<Execution | null> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM executions WHERE id = ?';
      
      this.db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row ? this.mapRowToExecution(row) : null);
        }
      });
    });
  }

  async findByPlanId(planId: string): Promise<Execution[]> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM executions WHERE plan_id = ? ORDER BY start_time DESC';
      
      this.db.all(query, [planId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToExecution(row)));
        }
      });
    });
  }

  async findAll(): Promise<Execution[]> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM executions ORDER BY start_time DESC';
      
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToExecution(row)));
        }
      });
    });
  }

  async count(): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT COUNT(*) as count FROM executions';
      
      this.db.get(query, [], (err, row: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count);
        }
      });
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Execution[]> {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM executions 
        WHERE start_time BETWEEN ? AND ?
        ORDER BY start_time DESC
      `;
      
      this.db.all(query, [startDate.toISOString(), endDate.toISOString()], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToExecution(row)));
        }
      });
    });
  }

  async deleteByDateRange(startDate: Date, endDate: Date): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM executions WHERE start_time BETWEEN ? AND ?`;
      
      this.db.run(query, [startDate.toISOString(), endDate.toISOString()], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async deleteOldest(count: number): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = `
        DELETE FROM executions 
        WHERE id IN (
          SELECT id FROM executions 
          ORDER BY start_time ASC 
          LIMIT ?
        )
      `;
      
      this.db.run(query, [count], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async update(id: string, data: Partial<CreateExecutionRequest>): Promise<void> {
    return new Promise((resolve, reject) => {
      const fields = [];
      const values = [];
      
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          values.push(value instanceof Date ? value.toISOString() : value);
        }
      }
      
      if (fields.length === 0) {
        resolve();
        return;
      }
      
      fields.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(id);
      
      const query = `UPDATE executions SET ${fields.join(', ')} WHERE id = ?`;
      
      this.db.run(query, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async delete(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM executions WHERE id = ?';
      
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  private mapRowToExecution(row: any): Execution {
    return {
      id: row.id,
      plan_id: row.plan_id,
      status: row.status,
      start_time: new Date(row.start_time),
      end_time: row.end_time ? new Date(row.end_time) : undefined,
      duration: row.duration,
      success_rate: row.success_rate,
      error_count: row.error_count,
      metadata: row.metadata,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }
}