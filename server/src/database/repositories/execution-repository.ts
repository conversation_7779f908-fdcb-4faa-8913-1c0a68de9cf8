/**
 * Execution repository for managing plan executions and steps
 */

import { BaseRepository } from './base-repository';
import { 
  Execution, 
  CreateExecutionRequest,
  ExecutionStep,
  CreateExecutionStepRequest,
  ExecutionFilter,
  ExecutionWithSteps,
  QueryResult 
} from '../models';
import { logger } from '../../utils/logger';

export class ExecutionRepository extends BaseRepository<Execution, CreateExecutionRequest> {
  protected readonly tableName = 'executions';
  protected readonly primaryKey = 'id';

  /**
   * Find execution by execution_id
   */
  public async findByExecutionId(executionId: string): Promise<Execution | null> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM executions WHERE execution_id = $1',
        [executionId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error finding execution by execution_id', {
        executionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find executions by plan_id
   */
  public async findByPlanId(planId: string, filter: ExecutionFilter = {}): Promise<QueryResult<Execution>> {
    try {
      const whereClause = this.buildWhereClause({ plan_id: planId, ...filter });
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT * FROM executions
        ${whereClause.clause}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding executions by plan_id', {
        planId,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find executions with their steps
   */
  public async findExecutionsWithSteps(
    filter: ExecutionFilter = {}
  ): Promise<QueryResult<ExecutionWithSteps>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT 
          e.*,
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', es.id,
              'step_number', es.step_number,
              'action_id', es.action_id,
              'action_type', es.action_type,
              'status', es.status,
              'log_message', es.log_message,
              'error_message', es.error_message,
              'execution_time_ms', es.execution_time_ms,
              'retry_count', es.retry_count,
              'created_at', es.created_at
            ) ORDER BY es.step_number
          ) as steps
        FROM executions e
        LEFT JOIN execution_steps es ON e.id = es.execution_id
        ${whereClause.clause}
        GROUP BY e.id
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding executions with steps', {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get execution statistics
   */
  public async getExecutionStats(timeframe: '24h' | '7d' | '30d' | '90d' = '24h'): Promise<{
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    partial_executions: number;
    avg_execution_time_ms: number;
    avg_success_rate: number;
    common_failure_types: { error_type: string; count: number }[];
  }> {
    try {
      const timeframeMap = {
        '24h': '24 hours',
        '7d': '7 days',
        '30d': '30 days',
        '90d': '90 days'
      };

      const interval = timeframeMap[timeframe];

      // Get basic stats
      const statsResult = await this.customQuery(`
        SELECT 
          COUNT(*) as total_executions,
          COUNT(CASE WHEN overall_status = 'success' THEN 1 END) as successful_executions,
          COUNT(CASE WHEN overall_status = 'failed' THEN 1 END) as failed_executions,
          COUNT(CASE WHEN overall_status = 'partial' THEN 1 END) as partial_executions,
          AVG(total_execution_time_ms) as avg_execution_time_ms,
          AVG(successful_steps::FLOAT / NULLIF(total_steps, 0)) as avg_success_rate
        FROM executions
        WHERE created_at >= NOW() - INTERVAL '${interval}'
      `);

      // Get common failure types
      const failureResult = await this.customQuery(`
        SELECT 
          SUBSTRING(error_message, 1, 100) as error_type,
          COUNT(*) as count
        FROM executions
        WHERE created_at >= NOW() - INTERVAL '${interval}'
        AND overall_status = 'failed'
        AND error_message IS NOT NULL
        GROUP BY SUBSTRING(error_message, 1, 100)
        ORDER BY count DESC
        LIMIT 10
      `);

      return {
        total_executions: parseInt(statsResult.rows[0].total_executions, 10),
        successful_executions: parseInt(statsResult.rows[0].successful_executions, 10),
        failed_executions: parseInt(statsResult.rows[0].failed_executions, 10),
        partial_executions: parseInt(statsResult.rows[0].partial_executions, 10),
        avg_execution_time_ms: parseFloat(statsResult.rows[0].avg_execution_time_ms) || 0,
        avg_success_rate: parseFloat(statsResult.rows[0].avg_success_rate) || 0,
        common_failure_types: failureResult.rows.map(row => ({
          error_type: row.error_type,
          count: parseInt(row.count, 10)
        }))
      };
    } catch (error) {
      logger.error('Error getting execution stats', {
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get executions by status
   */
  public async findByStatus(
    status: 'success' | 'failed' | 'partial',
    filter: ExecutionFilter = {}
  ): Promise<QueryResult<Execution>> {
    try {
      const statusFilter = { ...filter, overall_status: status };
      return await this.findAll(statusFilter, filter);
    } catch (error) {
      logger.error('Error finding executions by status', {
        status,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get execution performance trends
   */
  public async getPerformanceTrends(
    startDate: Date,
    endDate: Date,
    groupBy: 'hour' | 'day' | 'week' = 'day'
  ): Promise<{
    period: string;
    total_executions: number;
    successful_executions: number;
    avg_execution_time_ms: number;
    success_rate: number;
  }[]> {
    try {
      const formatMap = {
        hour: 'YYYY-MM-DD HH24:00',
        day: 'YYYY-MM-DD',
        week: 'YYYY-WW'
      };

      const dateFormat = formatMap[groupBy];

      const result = await this.customQuery(`
        SELECT 
          TO_CHAR(created_at, '${dateFormat}') as period,
          COUNT(*) as total_executions,
          COUNT(CASE WHEN overall_status = 'success' THEN 1 END) as successful_executions,
          AVG(total_execution_time_ms) as avg_execution_time_ms,
          (COUNT(CASE WHEN overall_status = 'success' THEN 1 END)::FLOAT / COUNT(*)) as success_rate
        FROM executions
        WHERE created_at >= $1 AND created_at <= $2
        GROUP BY TO_CHAR(created_at, '${dateFormat}')
        ORDER BY period
      `, [startDate, endDate]);

      return result.rows.map(row => ({
        period: row.period,
        total_executions: parseInt(row.total_executions, 10),
        successful_executions: parseInt(row.successful_executions, 10),
        avg_execution_time_ms: parseFloat(row.avg_execution_time_ms) || 0,
        success_rate: parseFloat(row.success_rate) || 0
      }));
    } catch (error) {
      logger.error('Error getting performance trends', {
        startDate,
        endDate,
        groupBy,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find long-running executions
   */
  public async findLongRunningExecutions(
    minDurationMs: number,
    filter: ExecutionFilter = {}
  ): Promise<QueryResult<Execution>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT * FROM executions
        ${whereClause.clause}
        ${whereClause.clause ? 'AND' : 'WHERE'} total_execution_time_ms >= $${whereClause.params.length + 1}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, minDurationMs, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding long-running executions', {
        minDurationMs,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Clean up old executions beyond retention period
   */
  public async cleanupOldExecutions(retentionDays: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM executions 
         WHERE created_at < NOW() - INTERVAL '${retentionDays} days'`
      );

      logger.info('Cleaned up old executions', {
        retentionDays,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error cleaning up old executions', {
        retentionDays,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find executions by date range for cleanup
   */
  public async findByDateRange(startDate: Date, endDate: Date): Promise<Execution[]> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM executions WHERE start_time BETWEEN $1 AND $2 ORDER BY start_time DESC',
        [startDate, endDate]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error finding executions by date range', {
        startDate,
        endDate,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete executions by date range
   */
  public async deleteByDateRange(startDate: Date, endDate: Date): Promise<number> {
    try {
      const result = await this.customQuery(
        'DELETE FROM executions WHERE start_time BETWEEN $1 AND $2',
        [startDate, endDate]
      );

      logger.info('Deleted executions by date range', {
        startDate,
        endDate,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error deleting executions by date range', {
        startDate,
        endDate,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete oldest executions
   */
  public async deleteOldest(count: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM executions 
         WHERE id IN (
           SELECT id FROM executions 
           ORDER BY start_time ASC 
           LIMIT $1
         )`,
        [count]
      );

      logger.info('Deleted oldest executions', {
        count,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error deleting oldest executions', {
        count,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}

/**
 * Execution Steps Repository
 */
export class ExecutionStepRepository extends BaseRepository<ExecutionStep, CreateExecutionStepRequest> {
  protected readonly tableName = 'execution_steps';
  protected readonly primaryKey = 'id';

  /**
   * Find steps by execution_id
   */
  public async findByExecutionId(executionId: string): Promise<QueryResult<ExecutionStep>> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM execution_steps WHERE execution_id = $1 ORDER BY step_number',
        [executionId]
      );

      return result;
    } catch (error) {
      logger.error('Error finding steps by execution_id', {
        executionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find failed steps
   */
  public async findFailedSteps(executionId?: string): Promise<QueryResult<ExecutionStep>> {
    try {
      const whereClause = executionId 
        ? 'WHERE execution_id = $1 AND status = \'failed\''
        : 'WHERE status = \'failed\'';
      
      const params = executionId ? [executionId] : [];
      
      const result = await this.customQuery(
        `SELECT * FROM execution_steps ${whereClause} ORDER BY created_at DESC`,
        params
      );

      return result;
    } catch (error) {
      logger.error('Error finding failed steps', {
        executionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get step statistics
   */
  public async getStepStats(timeframe: '24h' | '7d' | '30d' | '90d' = '24h'): Promise<{
    total_steps: number;
    successful_steps: number;
    failed_steps: number;
    avg_step_time_ms: number;
    common_action_types: { action_type: string; count: number; success_rate: number }[];
  }> {
    try {
      const timeframeMap = {
        '24h': '24 hours',
        '7d': '7 days',
        '30d': '30 days',
        '90d': '90 days'
      };

      const interval = timeframeMap[timeframe];

      // Get basic stats
      const statsResult = await this.customQuery(`
        SELECT 
          COUNT(*) as total_steps,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_steps,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_steps,
          AVG(execution_time_ms) as avg_step_time_ms
        FROM execution_steps
        WHERE created_at >= NOW() - INTERVAL '${interval}'
      `);

      // Get action type stats
      const actionResult = await this.customQuery(`
        SELECT 
          action_type,
          COUNT(*) as count,
          (COUNT(CASE WHEN status = 'success' THEN 1 END)::FLOAT / COUNT(*)) as success_rate
        FROM execution_steps
        WHERE created_at >= NOW() - INTERVAL '${interval}'
        AND action_type IS NOT NULL
        GROUP BY action_type
        ORDER BY count DESC
      `);

      return {
        total_steps: parseInt(statsResult.rows[0].total_steps, 10),
        successful_steps: parseInt(statsResult.rows[0].successful_steps, 10),
        failed_steps: parseInt(statsResult.rows[0].failed_steps, 10),
        avg_step_time_ms: parseFloat(statsResult.rows[0].avg_step_time_ms) || 0,
        common_action_types: actionResult.rows.map(row => ({
          action_type: row.action_type,
          count: parseInt(row.count, 10),
          success_rate: parseFloat(row.success_rate) || 0
        }))
      };
    } catch (error) {
      logger.error('Error getting step stats', {
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Clean up old execution steps beyond retention period
   */
  public async cleanupOldSteps(retentionDays: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM execution_steps 
         WHERE created_at < NOW() - INTERVAL '${retentionDays} days'`
      );

      logger.info('Cleaned up old execution steps', {
        retentionDays,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error cleaning up old execution steps', {
        retentionDays,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}