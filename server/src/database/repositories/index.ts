/**
 * Repository index file - exports all repositories
 */

export { BaseRepository } from './base-repository';
export { SessionRepository } from './session-repository';
export { PlanRepository } from './plan-repository';
export { ExecutionRepository, ExecutionStepRepository } from './execution-repository';

// Repository factory class for dependency injection
export class RepositoryFactory {
  private static sessionRepository: SessionRepository;
  private static planRepository: PlanRepository;
  private static executionRepository: ExecutionRepository;
  private static executionStepRepository: ExecutionStepRepository;

  public static getSessionRepository(): SessionRepository {
    if (!this.sessionRepository) {
      this.sessionRepository = new SessionRepository();
    }
    return this.sessionRepository;
  }

  public static getPlanRepository(): PlanRepository {
    if (!this.planRepository) {
      this.planRepository = new PlanRepository();
    }
    return this.planRepository;
  }

  public static getExecutionRepository(): ExecutionRepository {
    if (!this.executionRepository) {
      this.executionRepository = new ExecutionRepository();
    }
    return this.executionRepository;
  }

  public static getExecutionStepRepository(): ExecutionStepRepository {
    if (!this.executionStepRepository) {
      this.executionStepRepository = new ExecutionStepRepository();
    }
    return this.executionStepRepository;
  }

  // Reset repositories (for testing)
  public static reset(): void {
    this.sessionRepository = null as any;
    this.planRepository = null as any;
    this.executionRepository = null as any;
    this.executionStepRepository = null as any;
  }
}