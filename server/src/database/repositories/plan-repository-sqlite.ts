import { Database } from 'sqlite3';
import { database } from '../connection';
import { logger } from '../../utils/logger';

export interface Plan {
  id: string;
  user_instruction: string;
  dom_tree: string;
  generated_plan: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreatePlanRequest {
  user_instruction: string;
  dom_tree: string;
  generated_plan: string;
  status: string;
}

export class PlanRepository {
  private db: Database;

  constructor() {
    this.db = database;
  }

  async create(data: CreatePlanRequest): Promise<Plan> {
    return new Promise((resolve, reject) => {
      const id = require('uuid').v4();
      const now = new Date();
      
      const query = `
        INSERT INTO plans (id, user_instruction, dom_tree, generated_plan, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      this.db.run(query, [
        id,
        data.user_instruction,
        data.dom_tree,
        data.generated_plan,
        data.status,
        now.toISOString(),
        now.toISOString()
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            ...data,
            created_at: now,
            updated_at: now
          });
        }
      });
    });
  }

  async findById(id: string): Promise<Plan | null> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM plans WHERE id = ?';
      
      this.db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row ? this.mapRowToPlan(row) : null);
        }
      });
    });
  }

  async findAll(): Promise<Plan[]> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM plans ORDER BY created_at DESC';
      
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToPlan(row)));
        }
      });
    });
  }

  async count(): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = 'SELECT COUNT(*) as count FROM plans';
      
      this.db.get(query, [], (err, row: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count);
        }
      });
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Plan[]> {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM plans 
        WHERE created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
      `;
      
      this.db.all(query, [startDate.toISOString(), endDate.toISOString()], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => this.mapRowToPlan(row)));
        }
      });
    });
  }

  async deleteOldest(count: number): Promise<number> {
    return new Promise((resolve, reject) => {
      const query = `
        DELETE FROM plans 
        WHERE id IN (
          SELECT id FROM plans 
          ORDER BY created_at ASC 
          LIMIT ?
        )
      `;
      
      this.db.run(query, [count], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  async update(id: string, data: Partial<CreatePlanRequest>): Promise<void> {
    return new Promise((resolve, reject) => {
      const fields = [];
      const values = [];
      
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }
      
      if (fields.length === 0) {
        resolve();
        return;
      }
      
      fields.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(id);
      
      const query = `UPDATE plans SET ${fields.join(', ')} WHERE id = ?`;
      
      this.db.run(query, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async delete(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const query = 'DELETE FROM plans WHERE id = ?';
      
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  private mapRowToPlan(row: any): Plan {
    return {
      id: row.id,
      user_instruction: row.user_instruction,
      dom_tree: row.dom_tree,
      generated_plan: row.generated_plan,
      status: row.status,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }
}