/**
 * Plan repository for managing automation plans
 */

import { BaseRepository } from './base-repository';
import { 
  Plan, 
  CreatePlanRequest, 
  PlanFilter, 
  PlanWithExecution,
  QueryResult 
} from '../models';
import { logger } from '../../utils/logger';

export class PlanRepository extends BaseRepository<Plan, CreatePlanRequest> {
  protected readonly tableName = 'plans';
  protected readonly primaryKey = 'id';

  /**
   * Find plan by plan_id
   */
  public async findByPlanId(planId: string): Promise<Plan | null> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM plans WHERE plan_id = $1',
        [planId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error finding plan by plan_id', {
        planId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find plans by session_id
   */
  public async findBySessionId(sessionId: string, filter: PlanFilter = {}): Promise<QueryResult<Plan>> {
    try {
      const whereClause = this.buildWhereClause({ session_id: sessionId, ...filter });
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT * FROM plans
        ${whereClause.clause}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding plans by session_id', {
        sessionId,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find plans with their execution results
   */
  public async findPlansWithExecutions(
    filter: PlanFilter = {}
  ): Promise<QueryResult<PlanWithExecution>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT 
          p.*,
          e.execution_id,
          e.overall_status as execution_status,
          e.total_steps,
          e.successful_steps,
          e.failed_steps,
          e.error_message as execution_error,
          e.total_execution_time_ms,
          e.created_at as execution_created_at,
          s.session_id,
          s.user_id,
          d.dom_id,
          d.node_count
        FROM plans p
        LEFT JOIN executions e ON p.id = e.plan_id
        LEFT JOIN sessions s ON p.session_id = s.id
        LEFT JOIN dom_uploads d ON p.dom_upload_id = d.id
        ${whereClause.clause.replace('WHERE', 'WHERE p.')}
        ${orderByClause.replace('ORDER BY', 'ORDER BY p.')}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding plans with executions', {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get plan statistics
   */
  public async getPlanStats(timeframe: '24h' | '7d' | '30d' | '90d' = '24h'): Promise<{
    total_plans: number;
    successful_plans: number;
    failed_plans: number;
    avg_generation_time_ms: number;
    avg_cost_usd: number;
    total_cost_usd: number;
    avg_actions_per_plan: number;
    provider_usage: { provider: string; count: number; success_rate: number }[];
  }> {
    try {
      const timeframeMap = {
        '24h': '24 hours',
        '7d': '7 days',
        '30d': '30 days',
        '90d': '90 days'
      };

      const interval = timeframeMap[timeframe];

      // Get basic stats
      const statsResult = await this.customQuery(`
        SELECT 
          COUNT(*) as total_plans,
          COUNT(CASE WHEN validation_passed = true THEN 1 END) as successful_plans,
          COUNT(CASE WHEN validation_passed = false THEN 1 END) as failed_plans,
          AVG(generation_time_ms) as avg_generation_time_ms,
          AVG(cost_usd) as avg_cost_usd,
          SUM(cost_usd) as total_cost_usd,
          AVG(action_count) as avg_actions_per_plan
        FROM plans
        WHERE created_at >= NOW() - INTERVAL '${interval}'
      `);

      // Get provider usage stats
      const providerResult = await this.customQuery(`
        SELECT 
          provider_used as provider,
          COUNT(*) as count,
          (COUNT(CASE WHEN validation_passed = true THEN 1 END)::FLOAT / COUNT(*)) as success_rate
        FROM plans
        WHERE created_at >= NOW() - INTERVAL '${interval}'
        AND provider_used IS NOT NULL
        GROUP BY provider_used
        ORDER BY count DESC
      `);

      return {
        total_plans: parseInt(statsResult.rows[0].total_plans, 10),
        successful_plans: parseInt(statsResult.rows[0].successful_plans, 10),
        failed_plans: parseInt(statsResult.rows[0].failed_plans, 10),
        avg_generation_time_ms: parseFloat(statsResult.rows[0].avg_generation_time_ms) || 0,
        avg_cost_usd: parseFloat(statsResult.rows[0].avg_cost_usd) || 0,
        total_cost_usd: parseFloat(statsResult.rows[0].total_cost_usd) || 0,
        avg_actions_per_plan: parseFloat(statsResult.rows[0].avg_actions_per_plan) || 0,
        provider_usage: providerResult.rows.map(row => ({
          provider: row.provider,
          count: parseInt(row.count, 10),
          success_rate: parseFloat(row.success_rate) || 0
        }))
      };
    } catch (error) {
      logger.error('Error getting plan stats', {
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get plans by provider
   */
  public async findByProvider(
    provider: string, 
    filter: PlanFilter = {}
  ): Promise<QueryResult<Plan>> {
    try {
      const providerFilter = { ...filter, provider_used: provider };
      return await this.findAll(providerFilter, filter);
    } catch (error) {
      logger.error('Error finding plans by provider', {
        provider,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get most common instructions
   */
  public async getMostCommonInstructions(limit: number = 10): Promise<{
    instruction: string;
    count: number;
    success_rate: number;
  }[]> {
    try {
      const result = await this.customQuery(`
        SELECT 
          user_instruction as instruction,
          COUNT(*) as count,
          (COUNT(CASE WHEN validation_passed = true THEN 1 END)::FLOAT / COUNT(*)) as success_rate
        FROM plans
        WHERE created_at >= NOW() - INTERVAL '30 days'
        GROUP BY user_instruction
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT $1
      `, [limit]);

      return result.rows.map(row => ({
        instruction: row.instruction,
        count: parseInt(row.count, 10),
        success_rate: parseFloat(row.success_rate) || 0
      }));
    } catch (error) {
      logger.error('Error getting most common instructions', {
        limit,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get plans with high retry counts
   */
  public async findHighRetryPlans(
    minRetryCount: number = 3,
    filter: PlanFilter = {}
  ): Promise<QueryResult<Plan>> {
    try {
      const retryFilter = { ...filter, retry_count: minRetryCount };
      const whereClause = this.buildWhereClause(retryFilter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT * FROM plans
        ${whereClause.clause.replace('retry_count = $', 'retry_count >= $')}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding high retry plans', {
        minRetryCount,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get cost analysis by time period
   */
  public async getCostAnalysis(
    startDate: Date,
    endDate: Date,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ): Promise<{
    period: string;
    total_plans: number;
    total_cost_usd: number;
    avg_cost_per_plan: number;
    total_tokens: number;
  }[]> {
    try {
      const formatMap = {
        day: 'YYYY-MM-DD',
        week: 'YYYY-WW',
        month: 'YYYY-MM'
      };

      const dateFormat = formatMap[groupBy];

      const result = await this.customQuery(`
        SELECT 
          TO_CHAR(created_at, '${dateFormat}') as period,
          COUNT(*) as total_plans,
          SUM(cost_usd) as total_cost_usd,
          AVG(cost_usd) as avg_cost_per_plan,
          SUM(total_tokens) as total_tokens
        FROM plans
        WHERE created_at >= $1 AND created_at <= $2
        GROUP BY TO_CHAR(created_at, '${dateFormat}')
        ORDER BY period
      `, [startDate, endDate]);

      return result.rows.map(row => ({
        period: row.period,
        total_plans: parseInt(row.total_plans, 10),
        total_cost_usd: parseFloat(row.total_cost_usd) || 0,
        avg_cost_per_plan: parseFloat(row.avg_cost_per_plan) || 0,
        total_tokens: parseInt(row.total_tokens, 10) || 0
      }));
    } catch (error) {
      logger.error('Error getting cost analysis', {
        startDate,
        endDate,
        groupBy,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update plan with execution results
   */
  public async updatePlanWithExecution(
    planId: string,
    executionData: {
      execution_success: boolean;
      execution_time_ms?: number;
      steps_completed?: number;
      error_message?: string;
    }
  ): Promise<void> {
    try {
      await this.customQuery(`
        UPDATE plans 
        SET 
          updated_at = NOW(),
          -- Add custom fields if needed for execution tracking
        WHERE plan_id = $1
      `, [planId]);

      logger.info('Plan updated with execution results', {
        planId,
        executionData
      });
    } catch (error) {
      logger.error('Error updating plan with execution', {
        planId,
        executionData,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Clean up old plans beyond retention period
   */
  public async cleanupOldPlans(retentionDays: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM plans 
         WHERE created_at < NOW() - INTERVAL '${retentionDays} days'`
      );

      logger.info('Cleaned up old plans', {
        retentionDays,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error cleaning up old plans', {
        retentionDays,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find plans by date range for cleanup
   */
  public async findByDateRange(startDate: Date, endDate: Date): Promise<Plan[]> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM plans WHERE created_at BETWEEN $1 AND $2 ORDER BY created_at DESC',
        [startDate, endDate]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error finding plans by date range', {
        startDate,
        endDate,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete oldest plans
   */
  public async deleteOldest(count: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM plans 
         WHERE id IN (
           SELECT id FROM plans 
           ORDER BY created_at ASC 
           LIMIT $1
         )`,
        [count]
      );

      logger.info('Deleted oldest plans', {
        count,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error deleting oldest plans', {
        count,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}