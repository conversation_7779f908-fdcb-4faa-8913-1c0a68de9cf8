/**
 * Session repository for managing user sessions
 */

import { BaseRepository } from './base-repository';
import { 
  Session, 
  CreateSessionRequest, 
  SessionFilter, 
  SessionActivitySummary,
  QueryResult 
} from '../models';
import { logger } from '../../utils/logger';

export class SessionRepository extends BaseRepository<Session, CreateSessionRequest> {
  protected readonly tableName = 'sessions';
  protected readonly primaryKey = 'id';

  /**
   * Find session by session_id
   */
  public async findBySessionId(sessionId: string): Promise<Session | null> {
    try {
      const result = await this.customQuery(
        'SELECT * FROM sessions WHERE session_id = $1',
        [sessionId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error finding session by session_id', {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find sessions by user_id
   */
  public async findByUserId(userId: string, filter: SessionFilter = {}): Promise<QueryResult<Session>> {
    try {
      const whereClause = this.buildWhereClause({ user_id: userId, ...filter });
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT * FROM sessions
        ${whereClause.clause}
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error finding sessions by user_id', {
        userId,
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find active sessions
   */
  public async findActiveSessions(filter: SessionFilter = {}): Promise<QueryResult<Session>> {
    try {
      const activeFilter = { ...filter, is_active: true };
      return await this.findAll(activeFilter, filter);
    } catch (error) {
      logger.error('Error finding active sessions', {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Deactivate session
   */
  public async deactivateSession(sessionId: string): Promise<void> {
    try {
      await this.customQuery(
        'UPDATE sessions SET is_active = false, updated_at = NOW() WHERE session_id = $1',
        [sessionId]
      );

      logger.info('Session deactivated', { sessionId });
    } catch (error) {
      logger.error('Error deactivating session', {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get session activity summary
   */
  public async getSessionActivitySummary(
    filter: SessionFilter = {}
  ): Promise<QueryResult<SessionActivitySummary>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT 
          s.session_id,
          s.user_id,
          s.created_at as session_start,
          COUNT(DISTINCT p.id) as plans_generated,
          COUNT(DISTINCT e.id) as executions_attempted,
          COUNT(CASE WHEN e.overall_status = 'success' THEN 1 END) as successful_executions,
          COALESCE(MAX(GREATEST(e.created_at, p.created_at)), s.created_at) as last_activity
        FROM sessions s
        LEFT JOIN plans p ON s.id = p.session_id
        LEFT JOIN executions e ON s.id = e.session_id
        ${whereClause.clause}
        GROUP BY s.id, s.session_id, s.user_id, s.created_at
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error getting session activity summary', {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get session statistics
   */
  public async getSessionStats(timeframe: '24h' | '7d' | '30d' | '90d' = '24h'): Promise<{
    total_sessions: number;
    active_sessions: number;
    unique_users: number;
    avg_session_duration_ms: number;
    sessions_by_hour: { hour: number; count: number }[];
  }> {
    try {
      const timeframeMap = {
        '24h': '24 hours',
        '7d': '7 days',
        '30d': '30 days',
        '90d': '90 days'
      };

      const interval = timeframeMap[timeframe];

      // Get basic stats
      const statsResult = await this.customQuery(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN is_active THEN 1 END) as active_sessions,
          COUNT(DISTINCT user_id) as unique_users,
          AVG(EXTRACT(EPOCH FROM (updated_at - created_at)) * 1000) as avg_session_duration_ms
        FROM sessions
        WHERE created_at >= NOW() - INTERVAL '${interval}'
      `);

      // Get sessions by hour
      const hourlyResult = await this.customQuery(`
        SELECT 
          EXTRACT(HOUR FROM created_at) as hour,
          COUNT(*) as count
        FROM sessions
        WHERE created_at >= NOW() - INTERVAL '${interval}'
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
      `);

      return {
        total_sessions: parseInt(statsResult.rows[0].total_sessions, 10),
        active_sessions: parseInt(statsResult.rows[0].active_sessions, 10),
        unique_users: parseInt(statsResult.rows[0].unique_users, 10),
        avg_session_duration_ms: parseFloat(statsResult.rows[0].avg_session_duration_ms) || 0,
        sessions_by_hour: hourlyResult.rows.map(row => ({
          hour: parseInt(row.hour, 10),
          count: parseInt(row.count, 10)
        }))
      };
    } catch (error) {
      logger.error('Error getting session stats', {
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Cleanup inactive sessions older than specified days
   */
  public async cleanupInactiveSessions(daysOld: number): Promise<number> {
    try {
      const result = await this.customQuery(
        `DELETE FROM sessions 
         WHERE is_active = false 
         AND updated_at < NOW() - INTERVAL '${daysOld} days'`
      );

      logger.info('Cleaned up inactive sessions', {
        daysOld,
        deletedCount: result.count
      });

      return result.count || 0;
    } catch (error) {
      logger.error('Error cleaning up inactive sessions', {
        daysOld,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get sessions with their associated plans and executions
   */
  public async getSessionsWithActivity(
    filter: SessionFilter = {}
  ): Promise<QueryResult<Session & { plans_count: number; executions_count: number }>> {
    try {
      const whereClause = this.buildWhereClause(filter);
      const orderByClause = this.buildOrderByClause(filter);
      const limitClause = this.buildLimitClause(filter);

      const queryText = `
        SELECT 
          s.*,
          COUNT(DISTINCT p.id) as plans_count,
          COUNT(DISTINCT e.id) as executions_count
        FROM sessions s
        LEFT JOIN plans p ON s.id = p.session_id
        LEFT JOIN executions e ON s.id = e.session_id
        ${whereClause.clause}
        GROUP BY s.id
        ${orderByClause}
        ${limitClause.clause}
      `;

      const params = [...whereClause.params, ...limitClause.params];
      const result = await this.customQuery(queryText, params);

      return result;
    } catch (error) {
      logger.error('Error getting sessions with activity', {
        filter,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update session activity timestamp
   */
  public async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      await this.customQuery(
        'UPDATE sessions SET updated_at = NOW() WHERE session_id = $1',
        [sessionId]
      );
    } catch (error) {
      logger.error('Error updating session activity', {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}