-- SQLite Database Schema for Browser RPA System
-- Supports execution history, plans, analytics, and debugging

-- Plans table - stores generated automation plans
CREATE TABLE plans (
    id TEXT PRIMARY KEY,
    user_instruction TEXT NOT NULL,
    dom_tree TEXT NOT NULL,
    generated_plan TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Executions table - tracks plan execution attempts
CREATE TABLE executions (
    id TEXT PRIMARY KEY,
    plan_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME,
    duration INTEGER,
    success_rate REAL DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    metadata TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (plan_id) REFERENCES plans(id)
);

-- Actions table - individual actions within executions
CREATE TABLE actions (
    id TEXT PRIMARY KEY,
    execution_id TEXT NOT NULL,
    type TEXT NOT NULL,
    selector TEXT,
    value TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    duration INTEGER,
    error_message TEXT,
    screenshot_path TEXT,
    metadata TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (execution_id) REFERENCES executions(id)
);

-- Create indexes for performance
CREATE INDEX idx_plans_status ON plans(status);
CREATE INDEX idx_plans_created_at ON plans(created_at);

CREATE INDEX idx_executions_plan_id ON executions(plan_id);
CREATE INDEX idx_executions_status ON executions(status);
CREATE INDEX idx_executions_start_time ON executions(start_time);

CREATE INDEX idx_actions_execution_id ON actions(execution_id);
CREATE INDEX idx_actions_type ON actions(type);
CREATE INDEX idx_actions_status ON actions(status);
CREATE INDEX idx_actions_timestamp ON actions(timestamp);

-- Migration tracking table
CREATE TABLE migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
);