-- Database Schema for Browser RPA System
-- Supports execution history, plans, analytics, and debugging

-- Sessions table - tracks user sessions and context
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255), -- Optional user identification
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    screen_width INTEGER,
    screen_height INTEGER,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    
    -- Indexes for performance
    INDEX idx_sessions_session_id (session_id),
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_created_at (created_at)
);

-- DOM uploads table - tracks DOM tree uploads and metadata
CREATE TABLE dom_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dom_id VARCHAR(255) UNIQUE NOT NULL,
    session_id UUID REFERENCES sessions(id),
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    node_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- DOM analysis metadata
    form_count INTEGER DEFAULT 0,
    button_count INTEGER DEFAULT 0,
    input_count INTEGER DEFAULT 0,
    link_count INTEGER DEFAULT 0,
    image_count INTEGER DEFAULT 0,
    
    INDEX idx_dom_uploads_dom_id (dom_id),
    INDEX idx_dom_uploads_session_id (session_id),
    INDEX idx_dom_uploads_created_at (created_at)
);

-- Plans table - stores generated automation plans
CREATE TABLE plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id VARCHAR(255) UNIQUE NOT NULL,
    session_id UUID REFERENCES sessions(id),
    dom_upload_id UUID REFERENCES dom_uploads(id),
    
    -- Plan content
    user_instruction TEXT NOT NULL,
    action_summary TEXT,
    actions_json JSONB NOT NULL,
    action_count INTEGER,
    
    -- Generation metadata
    provider_used VARCHAR(100),
    model_used VARCHAR(100),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_usd DECIMAL(10, 6),
    generation_time_ms INTEGER,
    
    -- Retry and validation info
    retry_count INTEGER DEFAULT 0,
    validation_passed BOOLEAN,
    fallback_used BOOLEAN DEFAULT false,
    
    -- Screenshots
    screenshot_url VARCHAR(500),
    screenshot_file_path VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_plans_plan_id (plan_id),
    INDEX idx_plans_session_id (session_id),
    INDEX idx_plans_provider_used (provider_used),
    INDEX idx_plans_created_at (created_at),
    INDEX idx_plans_cost_usd (cost_usd)
);

-- Executions table - tracks plan execution attempts
CREATE TABLE executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    plan_id UUID REFERENCES plans(id),
    session_id UUID REFERENCES sessions(id),
    
    -- Execution results
    overall_status VARCHAR(50) NOT NULL, -- success, failed, partial
    total_steps INTEGER,
    successful_steps INTEGER,
    failed_steps INTEGER,
    
    -- Error information
    error_message TEXT,
    failed_step_id VARCHAR(255),
    failed_step_number INTEGER,
    
    -- Timing
    execution_start_time TIMESTAMP WITH TIME ZONE,
    execution_end_time TIMESTAMP WITH TIME ZONE,
    total_execution_time_ms INTEGER,
    
    -- Result file storage
    result_file_path VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_executions_execution_id (execution_id),
    INDEX idx_executions_plan_id (plan_id),
    INDEX idx_executions_session_id (session_id),
    INDEX idx_executions_overall_status (overall_status),
    INDEX idx_executions_created_at (created_at)
);

-- Execution steps table - individual step results with detailed info
CREATE TABLE execution_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id UUID REFERENCES executions(id),
    plan_id UUID REFERENCES plans(id),
    
    -- Step identification
    step_number INTEGER NOT NULL,
    action_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(100),
    
    -- Step execution details
    status VARCHAR(50) NOT NULL, -- success, failed, skipped
    log_message TEXT,
    error_message TEXT,
    
    -- Action details
    target_element_xpath TEXT,
    target_element_bbox JSONB,
    action_parameter TEXT,
    thought TEXT,
    
    -- Screenshot evidence
    screenshot_base64 TEXT,
    screenshot_file_path VARCHAR(500),
    
    -- Timing
    step_start_time TIMESTAMP WITH TIME ZONE,
    step_end_time TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,
    
    -- Retry information
    retry_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_execution_steps_execution_id (execution_id),
    INDEX idx_execution_steps_plan_id (plan_id),
    INDEX idx_execution_steps_step_number (step_number),
    INDEX idx_execution_steps_status (status),
    INDEX idx_execution_steps_action_type (action_type)
);

-- Provider metrics table - tracks LLM provider performance
CREATE TABLE provider_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Provider information
    provider_name VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    
    -- Request details
    request_type VARCHAR(50), -- plan_generation, health_check, etc.
    plan_id UUID REFERENCES plans(id),
    
    -- Performance metrics
    response_time_ms INTEGER,
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_usd DECIMAL(10, 6),
    
    -- Success/failure tracking
    success BOOLEAN NOT NULL,
    error_type VARCHAR(100),
    error_message TEXT,
    
    -- Health status
    provider_health_score DECIMAL(3, 2), -- 0.00 to 1.00
    circuit_breaker_triggered BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_provider_metrics_provider_name (provider_name),
    INDEX idx_provider_metrics_model_name (model_name),
    INDEX idx_provider_metrics_success (success),
    INDEX idx_provider_metrics_created_at (created_at),
    INDEX idx_provider_metrics_plan_id (plan_id)
);

-- System health table - overall system performance tracking
CREATE TABLE system_health (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    interval_type VARCHAR(20) NOT NULL, -- minute, hour, day, week, month
    
    -- Request volumes
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    
    -- Execution metrics
    total_executions INTEGER DEFAULT 0,
    successful_executions INTEGER DEFAULT 0,
    failed_executions INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time_ms DECIMAL(10, 2),
    avg_execution_time_ms DECIMAL(10, 2),
    avg_plan_generation_time_ms DECIMAL(10, 2),
    
    -- Cost metrics
    total_cost_usd DECIMAL(12, 6),
    total_tokens_used BIGINT,
    
    -- Error tracking
    error_rate DECIMAL(5, 4), -- 0.0000 to 1.0000
    top_error_types JSONB,
    
    -- Provider distribution
    provider_usage_stats JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_system_health_period_start (period_start),
    INDEX idx_system_health_interval_type (interval_type),
    INDEX idx_system_health_created_at (created_at)
);

-- User analytics table - tracks user behavior patterns
CREATE TABLE user_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User identification (optional)
    user_id VARCHAR(255),
    session_id UUID REFERENCES sessions(id),
    
    -- Behavior metrics
    total_sessions INTEGER DEFAULT 1,
    total_plans_generated INTEGER DEFAULT 0,
    total_executions INTEGER DEFAULT 0,
    total_successful_executions INTEGER DEFAULT 0,
    
    -- Usage patterns
    avg_instruction_length DECIMAL(8, 2),
    common_action_types JSONB,
    preferred_providers JSONB,
    
    -- Time patterns
    most_active_hours JSONB,
    session_duration_avg_ms BIGINT,
    
    -- Success patterns
    success_rate DECIMAL(5, 4),
    common_failure_types JSONB,
    
    -- First and last activity
    first_seen TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- Update tracking
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_user_analytics_user_id (user_id),
    INDEX idx_user_analytics_session_id (session_id),
    INDEX idx_user_analytics_last_seen (last_seen)
);

-- Configuration table - stores system configuration and feature flags
CREATE TABLE configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Configuration key-value pairs
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(50), -- string, number, boolean, json
    
    -- Description and metadata
    description TEXT,
    category VARCHAR(100), -- database, llm, execution, monitoring
    is_sensitive BOOLEAN DEFAULT false,
    
    -- Validation
    validation_rule TEXT, -- regex or validation function name
    default_value TEXT,
    
    -- Change tracking
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by VARCHAR(255),
    
    INDEX idx_configuration_config_key (config_key),
    INDEX idx_configuration_category (category)
);

-- Data retention policies table - manages data cleanup and archival
CREATE TABLE data_retention_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Policy details
    table_name VARCHAR(255) NOT NULL,
    retention_days INTEGER NOT NULL,
    archive_before_delete BOOLEAN DEFAULT true,
    
    -- Cleanup rules
    cleanup_condition TEXT, -- SQL WHERE clause condition
    archive_location VARCHAR(500),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    last_cleanup_run TIMESTAMP WITH TIME ZONE,
    next_cleanup_run TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_data_retention_table_name (table_name),
    INDEX idx_data_retention_next_cleanup (next_cleanup_run)
);

-- Update triggers for timestamp management
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to relevant tables
CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_analytics_updated_at BEFORE UPDATE ON user_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_configuration_updated_at BEFORE UPDATE ON configuration
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_retention_policies_updated_at BEFORE UPDATE ON data_retention_policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for common analytics queries

-- Daily execution summary view
CREATE VIEW daily_execution_summary AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_executions,
    COUNT(CASE WHEN overall_status = 'success' THEN 1 END) as successful_executions,
    COUNT(CASE WHEN overall_status = 'failed' THEN 1 END) as failed_executions,
    AVG(total_execution_time_ms) as avg_execution_time_ms,
    AVG(successful_steps::DECIMAL / NULLIF(total_steps, 0)) as avg_success_rate
FROM executions
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Provider performance view
CREATE VIEW provider_performance_summary AS
SELECT 
    provider_name,
    model_name,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN success THEN 1 END) as successful_requests,
    AVG(response_time_ms) as avg_response_time_ms,
    AVG(cost_usd) as avg_cost_usd,
    SUM(total_tokens) as total_tokens_used,
    AVG(provider_health_score) as avg_health_score
FROM provider_metrics
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY provider_name, model_name
ORDER BY successful_requests DESC;

-- Session activity view
CREATE VIEW session_activity_summary AS
SELECT 
    s.session_id,
    s.user_id,
    s.created_at as session_start,
    COUNT(DISTINCT p.id) as plans_generated,
    COUNT(DISTINCT e.id) as executions_attempted,
    COUNT(CASE WHEN e.overall_status = 'success' THEN 1 END) as successful_executions,
    MAX(COALESCE(e.created_at, p.created_at)) as last_activity
FROM sessions s
LEFT JOIN plans p ON s.id = p.session_id
LEFT JOIN executions e ON s.id = e.session_id
GROUP BY s.id, s.session_id, s.user_id, s.created_at
ORDER BY last_activity DESC;

-- Insert default configuration values
INSERT INTO configuration (config_key, config_value, config_type, description, category, default_value) VALUES
('database.connection_pool_size', '10', 'number', 'Database connection pool size', 'database', '10'),
('database.query_timeout_ms', '30000', 'number', 'Database query timeout in milliseconds', 'database', '30000'),
('retention.plans_days', '90', 'number', 'Days to retain plan records', 'retention', '90'),
('retention.executions_days', '180', 'number', 'Days to retain execution records', 'retention', '180'),
('retention.metrics_days', '365', 'number', 'Days to retain metrics data', 'retention', '365'),
('retention.screenshots_days', '30', 'number', 'Days to retain screenshot files', 'retention', '30'),
('monitoring.health_check_interval_ms', '300000', 'number', 'Provider health check interval in milliseconds', 'monitoring', '300000'),
('monitoring.enable_real_time_metrics', 'true', 'boolean', 'Enable real-time metrics collection', 'monitoring', 'true'),
('analytics.enable_user_tracking', 'true', 'boolean', 'Enable user behavior analytics', 'analytics', 'true'),
('analytics.session_timeout_ms', '1800000', 'number', 'Session timeout in milliseconds (30 minutes)', 'analytics', '1800000');