import { PlanRepository } from './repositories/plan-repository';
import { ExecutionRepository } from './repositories/execution-repository';
import { ActionRepository } from './repositories/action-repository';
import { logger } from '../utils/logger';

export async function seedDatabase(): Promise<void> {
  logger.info('Starting database seeding...');
  
  const planRepo = new PlanRepository();
  const executionRepo = new ExecutionRepository();
  const actionRepo = new ActionRepository();

  try {
    // Create sample plans
    const plan1 = await planRepo.create({
      user_instruction: 'Fill out a contact form',
      dom_tree: JSON.stringify({
        tagName: 'form',
        attributes: { id: 'contact-form' },
        children: [
          { tagName: 'input', attributes: { type: 'text', name: 'name' } },
          { tagName: 'input', attributes: { type: 'email', name: 'email' } },
          { tagName: 'textarea', attributes: { name: 'message' } }
        ]
      }),
      generated_plan: JSON.stringify({
        steps: [
          { type: 'type', selector: 'input[name="name"]', value: '<PERSON>' },
          { type: 'type', selector: 'input[name="email"]', value: '<EMAIL>' },
          { type: 'type', selector: 'textarea[name="message"]', value: 'Hello world' },
          { type: 'click', selector: 'button[type="submit"]' }
        ]
      }),
      status: 'pending'
    });

    const plan2 = await planRepo.create({
      user_instruction: 'Search for "automation tools"',
      dom_tree: JSON.stringify({
        tagName: 'div',
        attributes: { class: 'search-container' },
        children: [
          { tagName: 'input', attributes: { type: 'search', placeholder: 'Search...' } },
          { tagName: 'button', attributes: { type: 'submit' }, textContent: 'Search' }
        ]
      }),
      generated_plan: JSON.stringify({
        steps: [
          { type: 'type', selector: 'input[type="search"]', value: 'automation tools' },
          { type: 'click', selector: 'button[type="submit"]' }
        ]
      }),
      status: 'completed'
    });

    // Create sample executions
    const execution1 = await executionRepo.create({
      plan_id: plan1.id,
      status: 'completed',
      start_time: new Date(Date.now() - 300000), // 5 minutes ago
      end_time: new Date(Date.now() - 60000), // 1 minute ago
      duration: 240000, // 4 minutes
      success_rate: 100,
      error_count: 0,
      metadata: JSON.stringify({
        browser: 'Chrome',
        version: '120.0.0',
        viewport: { width: 1920, height: 1080 }
      })
    });

    const execution2 = await executionRepo.create({
      plan_id: plan2.id,
      status: 'failed',
      start_time: new Date(Date.now() - 600000), // 10 minutes ago
      end_time: new Date(Date.now() - 540000), // 9 minutes ago
      duration: 60000, // 1 minute
      success_rate: 50,
      error_count: 1,
      metadata: JSON.stringify({
        browser: 'Firefox',
        version: '119.0.0',
        viewport: { width: 1366, height: 768 }
      })
    });

    // Create sample actions
    await actionRepo.create({
      execution_id: execution1.id,
      type: 'type',
      selector: 'input[name="name"]',
      value: 'John Doe',
      status: 'success',
      timestamp: new Date(Date.now() - 280000),
      duration: 150,
      screenshot_path: '/screenshots/action1.png'
    });

    await actionRepo.create({
      execution_id: execution1.id,
      type: 'type',
      selector: 'input[name="email"]',
      value: '<EMAIL>',
      status: 'success',
      timestamp: new Date(Date.now() - 260000),
      duration: 120,
      screenshot_path: '/screenshots/action2.png'
    });

    await actionRepo.create({
      execution_id: execution2.id,
      type: 'click',
      selector: 'button[type="submit"]',
      status: 'failed',
      timestamp: new Date(Date.now() - 550000),
      duration: 5000,
      error_message: 'Element not found: button[type="submit"]',
      screenshot_path: '/screenshots/action3.png'
    });

    logger.info('Database seeding completed successfully');
    logger.info(`Created ${2} plans, ${2} executions, and ${3} actions`);
  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
}