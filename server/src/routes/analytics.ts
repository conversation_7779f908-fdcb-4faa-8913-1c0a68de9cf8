import express from 'express';
import { PlanRepository } from '../database/repositories/plan-repository';
import { ExecutionRepository } from '../database/repositories/execution-repository';
import { ActionRepository } from '../database/repositories/action-repository';
import { AnalyticsService } from '../services/analytics-service';
import { logger } from '../utils/logger';

const router = express.Router();
const analyticsService = new AnalyticsService();

router.get('/dashboard', async (req, res) => {
  try {
    const dashboard = await analyticsService.getDashboardStats();
    res.json(dashboard);
  } catch (error) {
    logger.error('Error getting dashboard stats:', error);
    res.status(500).json({ error: 'Failed to get dashboard stats' });
  }
});

router.get('/executions/stats', async (req, res) => {
  try {
    const { timeRange = '7d', groupBy = 'day' } = req.query;
    const stats = await analyticsService.getExecutionStats(timeRange as string, groupBy as string);
    res.json(stats);
  } catch (error) {
    logger.error('Error getting execution stats:', error);
    res.status(500).json({ error: 'Failed to get execution stats' });
  }
});

router.get('/plans/performance', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const performance = await analyticsService.getPlanPerformance(parseInt(limit as string));
    res.json(performance);
  } catch (error) {
    logger.error('Error getting plan performance:', error);
    res.status(500).json({ error: 'Failed to get plan performance' });
  }
});

router.get('/actions/frequency', async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;
    const frequency = await analyticsService.getActionFrequency(timeRange as string);
    res.json(frequency);
  } catch (error) {
    logger.error('Error getting action frequency:', error);
    res.status(500).json({ error: 'Failed to get action frequency' });
  }
});

router.get('/errors/analysis', async (req, res) => {
  try {
    const { timeRange = '7d', limit = 20 } = req.query;
    const errors = await analyticsService.getErrorAnalysis(timeRange as string, parseInt(limit as string));
    res.json(errors);
  } catch (error) {
    logger.error('Error getting error analysis:', error);
    res.status(500).json({ error: 'Failed to get error analysis' });
  }
});

router.get('/trends/success-rate', async (req, res) => {
  try {
    const { timeRange = '7d', groupBy = 'day' } = req.query;
    const trends = await analyticsService.getSuccessRateTrends(timeRange as string, groupBy as string);
    res.json(trends);
  } catch (error) {
    logger.error('Error getting success rate trends:', error);
    res.status(500).json({ error: 'Failed to get success rate trends' });
  }
});

export default router;