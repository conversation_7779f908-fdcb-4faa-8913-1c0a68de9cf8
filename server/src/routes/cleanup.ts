import express from 'express';
import { CleanupService } from '../services/cleanup-service';
import { logger } from '../utils/logger';

const router = express.Router();

// Default cleanup configuration
const defaultConfig = {
  retentionPeriodDays: parseInt(process.env.RETENTION_PERIOD_DAYS || '30'),
  maxRecordsPerTable: parseInt(process.env.MAX_RECORDS_PER_TABLE || '10000'),
  cleanupScreenshots: process.env.CLEANUP_SCREENSHOTS !== 'false',
  cleanupInterval: parseInt(process.env.CLEANUP_INTERVAL || '86400000') // 24 hours
};

const cleanupService = new CleanupService(defaultConfig);

// Start scheduled cleanup if enabled
if (process.env.ENABLE_SCHEDULED_CLEANUP === 'true') {
  cleanupService.startScheduledCleanup();
}

router.post('/run', async (req, res) => {
  try {
    logger.info('Manual cleanup requested');
    const result = await cleanupService.performCleanup();
    res.json({
      success: true,
      result
    });
  } catch (error) {
    logger.error('Manual cleanup failed:', error);
    res.status(500).json({
      success: false,
      error: 'Cleanup failed'
    });
  }
});

router.get('/preview', async (req, res) => {
  try {
    const preview = await cleanupService.getCleanupPreview();
    res.json({
      success: true,
      preview,
      config: defaultConfig
    });
  } catch (error) {
    logger.error('Failed to get cleanup preview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cleanup preview'
    });
  }
});

router.get('/status', async (req, res) => {
  try {
    res.json({
      success: true,
      config: defaultConfig,
      scheduledCleanupEnabled: process.env.ENABLE_SCHEDULED_CLEANUP === 'true'
    });
  } catch (error) {
    logger.error('Failed to get cleanup status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cleanup status'
    });
  }
});

export default router;