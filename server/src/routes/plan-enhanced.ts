import fs from 'node:fs';
import path from 'node:path';
import express from 'express';
import type { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { generatePlan } from '../services/planService';
import type { GeneratePlanRequest, UploadResultRequest } from '../types';

const router = express.Router();

// Enhanced error handling middleware
const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Request validation middleware
const validateRequest = (schema: any) => (req: Request, res: Response, next: Function) => {
  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: 'Invalid request data', 
      details: error.details.map((d: any) => d.message) 
    });
  }
  next();
};

// Rate limiting (simple implementation)
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 10; // requests per minute
const RATE_WINDOW = 60000; // 1 minute

const rateLimit = (req: Request, res: Response, next: Function) => {
  const clientId = req.ip || 'unknown';
  const now = Date.now();
  const client = requestCounts.get(clientId);
  
  if (!client || now > client.resetTime) {
    requestCounts.set(clientId, { count: 1, resetTime: now + RATE_WINDOW });
    return next();
  }
  
  if (client.count >= RATE_LIMIT) {
    return res.status(429).json({ 
      error: 'Rate limit exceeded', 
      retryAfter: Math.ceil((client.resetTime - now) / 1000) 
    });
  }
  
  client.count++;
  next();
};

/**
 * 上传截图，返回URL - with enhanced error handling
 */
router.post('/upload/screenshot', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const { base64Image } = req.body as { base64Image: string };
  
  if (!base64Image) {
    return res.status(400).json({ error: 'Missing base64Image' });
  }
  
  // Validate base64 format
  if (!base64Image.startsWith('data:image/') && !isValidBase64(base64Image)) {
    return res.status(400).json({ error: 'Invalid base64 image format' });
  }
  
  // Check file size (limit to 5MB)
  const sizeInBytes = (base64Image.length * 3) / 4;
  if (sizeInBytes > 5 * 1024 * 1024) {
    return res.status(400).json({ error: 'Image too large (max 5MB)' });
  }
  
  try {
    const buffer = Buffer.from(base64Image, 'base64');
    const filename = `screenshot-${uuidv4()}.png`;
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, filename);
    
    // Ensure directory exists
    await fs.promises.mkdir(saveDir, { recursive: true });
    
    // Save file
    await fs.promises.writeFile(savePath, buffer);
    
    const url = `/uploads/${filename}`;
    console.log(`📸 Screenshot uploaded: ${filename}`);
    
    res.json({ url, filename, size: sizeInBytes });
  } catch (error) {
    console.error('Screenshot upload error:', error);
    res.status(500).json({ 
      error: 'Failed to upload screenshot', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}));

/**
 * 上传DOM树，返回domId - with enhanced error handling
 */
router.post('/upload/dom', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const { domTree } = req.body as { domTree: any };
  
  if (!domTree) {
    return res.status(400).json({ error: 'Missing domTree' });
  }
  
  // Validate DOM tree structure
  if (!domTree.rootId || !domTree.map) {
    return res.status(400).json({ error: 'Invalid DOM tree structure' });
  }
  
  // Check payload size (limit to 10MB)
  const payloadSize = JSON.stringify(domTree).length;
  if (payloadSize > 10 * 1024 * 1024) {
    return res.status(400).json({ error: 'DOM tree too large (max 10MB)' });
  }
  
  try {
    const domId = uuidv4();
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, `${domId}.json`);
    
    // Ensure directory exists
    await fs.promises.mkdir(saveDir, { recursive: true });
    
    // Save DOM tree with metadata
    const domData = {
      id: domId,
      timestamp: new Date().toISOString(),
      size: payloadSize,
      nodeCount: Object.keys(domTree.map).length,
      domTree,
    };
    
    await fs.promises.writeFile(savePath, JSON.stringify(domData, null, 2), 'utf-8');
    
    console.log(`🌳 DOM tree uploaded: ${domId} (${payloadSize} bytes, ${domData.nodeCount} nodes)`);
    
    res.json({ domId, size: payloadSize, nodeCount: domData.nodeCount });
  } catch (error) {
    console.error('DOM tree upload error:', error);
    res.status(500).json({ 
      error: 'Failed to upload DOM tree', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}));

/**
 * 生成Plan - with enhanced error handling
 */
router.post('/generate', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    const body = req.body as GeneratePlanRequest;
    
    // Validate required fields
    if (!body.userInstruction || !body.conversationHistory || !body.size) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Validate instruction length
    if (body.userInstruction.length > 1000) {
      return res.status(400).json({ error: 'User instruction too long (max 1000 characters)' });
    }
    
    console.log(`🎯 Generating plan for: "${body.userInstruction}"`);
    
    const plan = await generatePlan(body);
    
    // Attach screenshot URL if provided
    if (body.screenshotUrl) {
      plan.screenshotUrl = body.screenshotUrl;
    }
    
    const executionTime = Date.now() - startTime;
    console.log(`✅ Plan generated successfully in ${executionTime}ms: ${plan.actions.length} actions`);
    
    res.json({ 
      ...plan, 
      metadata: { 
        executionTime, 
        timestamp: new Date().toISOString(),
        actionCount: plan.actions.length,
      } 
    });
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`❌ Plan generation failed after ${executionTime}ms:`, error);
    
    // Determine error type and status code
    let statusCode = 500;
    let errorMessage = 'Failed to generate plan';
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        statusCode = 504;
        errorMessage = 'Plan generation timeout';
      } else if (error.message.includes('rate limit')) {
        statusCode = 429;
        errorMessage = 'Rate limit exceeded';
      } else if (error.message.includes('invalid')) {
        statusCode = 400;
        errorMessage = 'Invalid request data';
      }
    }
    
    res.status(statusCode).json({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : 'Unknown error',
      executionTime 
    });
  }
}));

/**
 * 上传执行结果 - with enhanced error handling
 */
router.post('/result', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  try {
    const body = req.body as UploadResultRequest;
    
    // Validate required fields
    if (!body.planId || !body.results || !body.overallStatus) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Validate results structure
    if (!Array.isArray(body.results)) {
      return res.status(400).json({ error: 'Results must be an array' });
    }
    
    // Save execution results to file for analysis
    const resultId = uuidv4();\n    const saveDir = path.join(__dirname, '../../../uploads/results');\n    const savePath = path.join(saveDir, `result-${resultId}.json`);\n    \n    await fs.promises.mkdir(saveDir, { recursive: true });\n    \n    const resultData = {\n      id: resultId,\n      timestamp: new Date().toISOString(),\n      planId: body.planId,\n      overallStatus: body.overallStatus,\n      stepCount: body.results.length,\n      successCount: body.results.filter(r => r.status === 'success').length,\n      errorMessage: body.errorMessage,\n      results: body.results,\n    };\n    \n    await fs.promises.writeFile(savePath, JSON.stringify(resultData, null, 2), 'utf-8');\n    \n    console.log(`📊 Execution result saved: ${resultId} (${body.overallStatus}) - ${resultData.successCount}/${resultData.stepCount} steps succeeded`);\n    \n    res.json({ \n      success: true, \n      message: 'Result saved successfully',\n      resultId,\n      summary: {\n        status: body.overallStatus,\n        totalSteps: resultData.stepCount,\n        successfulSteps: resultData.successCount,\n        failedSteps: resultData.stepCount - resultData.successCount,\n      }\n    });\n  } catch (error) {\n    console.error('Result upload error:', error);\n    res.status(500).json({ \n      error: 'Failed to upload result', \n      details: error instanceof Error ? error.message : 'Unknown error' \n    });\n  }\n}));\n\n/**\n * Health check endpoint\n */\nrouter.get('/health', (req: Request, res: Response) => {\n  res.json({ \n    status: 'healthy', \n    timestamp: new Date().toISOString(),\n    uptime: process.uptime(),\n    memory: process.memoryUsage(),\n  });\n});\n\n/**\n * Get execution statistics\n */\nrouter.get('/stats', asyncHandler(async (req: Request, res: Response) => {\n  try {\n    const resultsDir = path.join(__dirname, '../../../uploads/results');\n    \n    if (!fs.existsSync(resultsDir)) {\n      return res.json({ totalExecutions: 0, successRate: 0 });\n    }\n    \n    const files = await fs.promises.readdir(resultsDir);\n    const resultFiles = files.filter(f => f.startsWith('result-') && f.endsWith('.json'));\n    \n    let totalExecutions = 0;\n    let successfulExecutions = 0;\n    \n    for (const file of resultFiles) {\n      try {\n        const filePath = path.join(resultsDir, file);\n        const data = JSON.parse(await fs.promises.readFile(filePath, 'utf-8'));\n        \n        totalExecutions++;\n        if (data.overallStatus === 'success') {\n          successfulExecutions++;\n        }\n      } catch (error) {\n        console.error(`Error reading result file ${file}:`, error);\n      }\n    }\n    \n    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;\n    \n    res.json({\n      totalExecutions,\n      successfulExecutions,\n      failedExecutions: totalExecutions - successfulExecutions,\n      successRate: Math.round(successRate * 100) / 100,\n    });\n  } catch (error) {\n    console.error('Stats retrieval error:', error);\n    res.status(500).json({ error: 'Failed to retrieve statistics' });\n  }\n}));\n\n// Global error handler\nrouter.use((error: Error, req: Request, res: Response, next: Function) => {\n  console.error('Unhandled error:', error);\n  res.status(500).json({ \n    error: 'Internal server error', \n    details: process.env.NODE_ENV === 'development' ? error.message : 'Contact support',\n    timestamp: new Date().toISOString(),\n  });\n});\n\n/**\n * Helper function to validate base64 string\n */\nfunction isValidBase64(str: string): boolean {\n  try {\n    return Buffer.from(str, 'base64').toString('base64') === str;\n  } catch (error) {\n    return false;\n  }\n}\n\nexport default router;