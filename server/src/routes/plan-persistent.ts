/**
 * Enhanced plan routes with database persistence
 * Integrates with the persistent plan service for complete data storage
 */

import fs from 'node:fs';
import path from 'node:path';
import express from 'express';
import type { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { 
  generatePlan, 
  generatePlanWithMetadata,
  saveExecutionResults,
  saveDomUpload,
  getExecutionHistory,
  getPlanAnalytics
} from '../services/planService-persistent';
import { initializeDatabase } from '../database/connection';
import { RepositoryFactory } from '../database/repositories';
import type { GeneratePlanRequest, UploadResultRequest } from '../types';
import { logger } from '../utils/logger';

const router = express.Router();

// Initialize database connection
initializeDatabase().catch(error => {
  logger.error('Failed to initialize database', { error: error.message });
  process.exit(1);
});

// Enhanced error handling middleware
const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Request validation middleware
const validateRequest = (schema: any) => (req: Request, res: Response, next: Function) => {
  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: 'Invalid request data', 
      details: error.details.map((d: any) => d.message) 
    });
  }
  next();
};

// Rate limiting (simple implementation)
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 10; // requests per minute
const RATE_WINDOW = 60000; // 1 minute

const rateLimit = (req: Request, res: Response, next: Function) => {
  const clientId = req.ip || 'unknown';
  const now = Date.now();
  const client = requestCounts.get(clientId);
  
  if (!client || now > client.resetTime) {
    requestCounts.set(clientId, { count: 1, resetTime: now + RATE_WINDOW });
    return next();
  }
  
  if (client.count >= RATE_LIMIT) {
    return res.status(429).json({ 
      error: 'Rate limit exceeded', 
      retryAfter: Math.ceil((client.resetTime - now) / 1000) 
    });
  }
  
  client.count++;
  next();
};

/**
 * Upload screenshot with persistence
 */
router.post('/upload/screenshot', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const { base64Image, sessionId } = req.body as { base64Image: string; sessionId?: string };
  
  if (!base64Image) {
    return res.status(400).json({ error: 'Missing base64Image' });
  }
  
  // Validate base64 format
  if (!base64Image.startsWith('data:image/') && !isValidBase64(base64Image)) {
    return res.status(400).json({ error: 'Invalid base64 image format' });
  }
  
  // Check file size (limit to 5MB)
  const sizeInBytes = (base64Image.length * 3) / 4;
  if (sizeInBytes > 5 * 1024 * 1024) {
    return res.status(400).json({ error: 'Image too large (max 5MB)' });
  }
  
  try {
    const buffer = Buffer.from(base64Image, 'base64');
    const filename = `screenshot-${uuidv4()}.png`;
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, filename);
    
    // Ensure directory exists
    await fs.promises.mkdir(saveDir, { recursive: true });
    
    // Save file
    await fs.promises.writeFile(savePath, buffer);
    
    const url = `/uploads/${filename}`;
    
    logger.info('Screenshot uploaded', {
      filename,
      size: sizeInBytes,
      sessionId: sessionId || 'unknown'
    });
    
    res.json({ 
      url, 
      filename, 
      size: sizeInBytes,
      success: true 
    });
  } catch (error) {
    logger.error('Screenshot upload error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: sessionId || 'unknown'
    });
    
    res.status(500).json({ 
      error: 'Failed to upload screenshot', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}));

/**
 * Upload DOM tree with persistence
 */
router.post('/upload/dom', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const { domTree, sessionId } = req.body as { domTree: any; sessionId?: string };
  
  if (!domTree) {
    return res.status(400).json({ error: 'Missing domTree' });
  }
  
  // Validate DOM tree structure
  if (!domTree.rootId || !domTree.map) {
    return res.status(400).json({ error: 'Invalid DOM tree structure' });
  }
  
  // Check payload size (limit to 10MB)
  const payloadSize = JSON.stringify(domTree).length;
  if (payloadSize > 10 * 1024 * 1024) {
    return res.status(400).json({ error: 'DOM tree too large (max 10MB)' });
  }
  
  try {
    const domId = uuidv4();
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, `${domId}.json`);
    
    // Ensure directory exists
    await fs.promises.mkdir(saveDir, { recursive: true });
    
    // Count different element types for analytics
    const elementCounts = countDomElements(domTree);
    
    // Save DOM tree with metadata
    const domData = {
      id: domId,
      timestamp: new Date().toISOString(),
      size: payloadSize,
      nodeCount: Object.keys(domTree.map).length,
      domTree,
      elementCounts,
    };
    
    await fs.promises.writeFile(savePath, JSON.stringify(domData, null, 2), 'utf-8');
    
    // Save to database
    await saveDomUpload(domId, sessionId || 'default-session', savePath, {
      fileSize: payloadSize,
      nodeCount: domData.nodeCount,
      formCount: elementCounts.forms,
      buttonCount: elementCounts.buttons,
      inputCount: elementCounts.inputs,
      linkCount: elementCounts.links,
      imageCount: elementCounts.images
    });
    
    logger.info('DOM tree uploaded', {
      domId,
      size: payloadSize,
      nodeCount: domData.nodeCount,
      sessionId: sessionId || 'default-session',
      elementCounts
    });
    
    res.json({ 
      domId, 
      size: payloadSize, 
      nodeCount: domData.nodeCount,
      elementCounts,
      success: true 
    });
  } catch (error) {
    logger.error('DOM tree upload error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: sessionId || 'default-session'
    });
    
    res.status(500).json({ 
      error: 'Failed to upload DOM tree', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}));

/**
 * Generate plan with persistence
 */
router.post('/generate', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    const body = req.body as GeneratePlanRequest;
    
    // Validate required fields
    if (!body.userInstruction || !body.conversationHistory || !body.size) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Validate instruction length
    if (body.userInstruction.length > 1000) {
      return res.status(400).json({ error: 'User instruction too long (max 1000 characters)' });
    }
    
    logger.info('Generating plan', {
      userInstruction: body.userInstruction.substring(0, 100) + '...',
      sessionId: body.sessionId || 'default-session',
      domId: body.domId
    });
    
    // Generate plan with metadata and persistence
    const result = await generatePlanWithMetadata(body);
    
    const executionTime = Date.now() - startTime;
    
    logger.info('Plan generated successfully', {
      planId: result.plan.planId,
      actionsCount: result.plan.actions.length,
      executionTime,
      provider: result.metadata.provider,
      cost: result.metadata.cost,
      sessionId: body.sessionId || 'default-session'
    });
    
    res.json({ 
      ...result.plan,
      metadata: { 
        ...result.metadata,
        executionTime, 
        timestamp: new Date().toISOString(),
        actionCount: result.plan.actions.length,
      },
      success: true 
    });
  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    logger.error('Plan generation failed', {
      executionTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      sessionId: req.body.sessionId || 'default-session'
    });
    
    // Determine error type and status code
    let statusCode = 500;
    let errorMessage = 'Failed to generate plan';
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        statusCode = 504;
        errorMessage = 'Plan generation timeout';
      } else if (error.message.includes('rate limit')) {
        statusCode = 429;
        errorMessage = 'Rate limit exceeded';
      } else if (error.message.includes('invalid')) {
        statusCode = 400;
        errorMessage = 'Invalid request data';
      }
    }
    
    res.status(statusCode).json({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : 'Unknown error',
      executionTime,
      success: false 
    });
  }
}));

/**
 * Upload execution results with persistence
 */
router.post('/result', rateLimit, asyncHandler(async (req: Request, res: Response) => {
  try {
    const body = req.body as UploadResultRequest;
    
    // Validate required fields
    if (!body.planId || !body.results || !body.overallStatus) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Validate results structure
    if (!Array.isArray(body.results)) {
      return res.status(400).json({ error: 'Results must be an array' });
    }
    
    // Save execution results to database
    await saveExecutionResults(body);
    
    // Also save to file for backward compatibility
    const resultId = uuidv4();
    const saveDir = path.join(__dirname, '../../../uploads/results');
    const savePath = path.join(saveDir, `result-${resultId}.json`);
    
    await fs.promises.mkdir(saveDir, { recursive: true });
    
    const resultData = {
      id: resultId,
      timestamp: new Date().toISOString(),
      planId: body.planId,
      sessionId: body.sessionId || 'default-session',
      overallStatus: body.overallStatus,
      stepCount: body.results.length,
      successCount: body.results.filter(r => r.status === 'success').length,
      errorMessage: body.errorMessage,
      results: body.results,
    };
    
    await fs.promises.writeFile(savePath, JSON.stringify(resultData, null, 2), 'utf-8');
    
    logger.info('Execution result saved', {
      resultId,
      planId: body.planId,
      sessionId: body.sessionId || 'default-session',
      overallStatus: body.overallStatus,
      successCount: resultData.successCount,
      stepCount: resultData.stepCount
    });
    
    res.json({ 
      success: true, 
      message: 'Result saved successfully',
      resultId,
      summary: {
        status: body.overallStatus,
        totalSteps: resultData.stepCount,
        successfulSteps: resultData.successCount,
        failedSteps: resultData.stepCount - resultData.successCount,
      }
    });
  } catch (error) {
    logger.error('Result upload error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      planId: req.body.planId,
      sessionId: req.body.sessionId || 'default-session'
    });
    
    res.status(500).json({ 
      error: 'Failed to upload result', 
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false 
    });
  }
}));

/**
 * Get execution history for a session
 */
router.get('/history/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    
    const history = await getExecutionHistory(sessionId, limit);
    
    res.json({
      sessionId,
      history,
      count: history.length,
      success: true
    });
  } catch (error) {
    logger.error('Error retrieving execution history', {
      sessionId: req.params.sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      error: 'Failed to retrieve execution history',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
}));

/**
 * Get analytics data
 */
router.get('/analytics', asyncHandler(async (req: Request, res: Response) => {
  try {
    const timeframe = (req.query.timeframe as '24h' | '7d' | '30d' | '90d') || '24h';
    
    const analytics = await getPlanAnalytics(timeframe);
    
    res.json({
      timeframe,
      analytics,
      success: true
    });
  } catch (error) {
    logger.error('Error retrieving analytics', {
      timeframe: req.query.timeframe,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      error: 'Failed to retrieve analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
}));

/**
 * Get session summary
 */
router.get('/session/:sessionId/summary', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    
    const sessionRepo = RepositoryFactory.getSessionRepository();
    const planRepo = RepositoryFactory.getPlanRepository();
    const executionRepo = RepositoryFactory.getExecutionRepository();
    
    const session = await sessionRepo.findBySessionId(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }
    
    const plans = await planRepo.findBySessionId(session.id);
    const executions = await executionRepo.findBySessionId(session.id);
    
    const summary = {
      session: {
        id: session.session_id,
        created_at: session.created_at,
        is_active: session.is_active,
        user_id: session.user_id
      },
      plans: {
        count: plans.count,
        successful: plans.rows.filter(p => p.validation_passed).length,
        failed: plans.rows.filter(p => p.validation_passed === false).length,
        total_cost: plans.rows.reduce((sum, p) => sum + (p.cost_usd || 0), 0)
      },
      executions: {
        count: executions.count,
        successful: executions.rows.filter(e => e.overall_status === 'success').length,
        failed: executions.rows.filter(e => e.overall_status === 'failed').length,
        partial: executions.rows.filter(e => e.overall_status === 'partial').length
      }
    };
    
    res.json({
      sessionId,
      summary,
      success: true
    });
  } catch (error) {
    logger.error('Error retrieving session summary', {
      sessionId: req.params.sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      error: 'Failed to retrieve session summary',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
}));

/**
 * Health check endpoint with database status
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { checkDatabaseHealth } = await import('../database/connection');
    const dbHealth = await checkDatabaseHealth();
    
    res.json({ 
      status: dbHealth.healthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: dbHealth
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Database health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * Get comprehensive statistics
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const timeframe = (req.query.timeframe as '24h' | '7d' | '30d' | '90d') || '24h';
    
    const planRepo = RepositoryFactory.getPlanRepository();
    const executionRepo = RepositoryFactory.getExecutionRepository();
    const sessionRepo = RepositoryFactory.getSessionRepository();
    
    const [planStats, executionStats, sessionStats] = await Promise.all([
      planRepo.getPlanStats(timeframe),
      executionRepo.getExecutionStats(timeframe),
      sessionRepo.getSessionStats(timeframe)
    ]);
    
    res.json({
      timeframe,
      plans: planStats,
      executions: executionStats,
      sessions: sessionStats,
      success: true
    });
  } catch (error) {
    logger.error('Stats retrieval error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({ 
      error: 'Failed to retrieve statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false 
    });
  }
}));

// Global error handler
router.use((error: Error, req: Request, res: Response, next: Function) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  });
  
  res.status(500).json({ 
    error: 'Internal server error', 
    details: process.env.NODE_ENV === 'development' ? error.message : 'Contact support',
    timestamp: new Date().toISOString(),
    success: false
  });
});

/**
 * Helper function to validate base64 string
 */
function isValidBase64(str: string): boolean {
  try {
    return Buffer.from(str, 'base64').toString('base64') === str;
  } catch (error) {
    return false;
  }
}

/**
 * Helper function to count DOM elements
 */
function countDomElements(domTree: any): {
  forms: number;
  buttons: number;
  inputs: number;
  links: number;
  images: number;
} {
  const counts = {
    forms: 0,
    buttons: 0,
    inputs: 0,
    links: 0,
    images: 0
  };

  if (domTree.map) {
    Object.values(domTree.map).forEach((node: any) => {
      const tagName = node.tagName?.toLowerCase();
      
      switch (tagName) {
        case 'form':
          counts.forms++;
          break;
        case 'button':
          counts.buttons++;
          break;
        case 'input':
          counts.inputs++;
          break;
        case 'a':
          counts.links++;
          break;
        case 'img':
          counts.images++;
          break;
      }
    });
  }

  return counts;
}

export default router;