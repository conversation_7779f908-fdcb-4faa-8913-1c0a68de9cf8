import fs from 'node:fs';
import path from 'node:path';
import express from 'express';
import type { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { generatePlan } from '../services/planService';
import type { GeneratePlanRequest, UploadResultRequest } from '../types';

const router = express.Router();

/**
 * 上传截图，返回URL
 */
router.post('/upload/screenshot', async (req: Request, res: Response) => {
  try {
    const { base64Image } = req.body as { base64Image: string };
    if (!base64Image) {
      return res.status(400).json({ error: 'Missing base64Image' });
    }

    const buffer = Buffer.from(base64Image, 'base64');
    const filename = `${uuidv4()}.png`;
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, filename);

    fs.mkdirSync(saveDir, { recursive: true });
    fs.writeFileSync(savePath, buffer);

    const url = `/uploads/${filename}`;
    res.json({ url });
  } catch (err) {
    console.error('upload screenshot error', err);
    res.status(500).json({ error: 'Failed to upload screenshot' });
  }
});
/**
 * 上传DOM树，返回domId
 */
router.post('/upload/dom', async (req: Request, res: Response) => {
  try {
    const { domTree } = req.body as { domTree: any };
    if (!domTree) {
      return res.status(400).json({ error: 'Missing domTree' });
    }
    const domId = uuidv4();
    // 可选：将 domTree 存储到本地文件，方便后续调试
    const saveDir = path.join(__dirname, '../../../uploads');
    const savePath = path.join(saveDir, `${domId}.json`);
    fs.mkdirSync(saveDir, { recursive: true });
    fs.writeFileSync(savePath, JSON.stringify(domTree, null, 2), 'utf-8');
    res.json({ domId });
  } catch (err) {
    console.error('upload dom tree error', err);
    res.status(500).json({ error: 'Failed to upload dom tree' });
  }
});

/**
 * 生成Plan
 */
router.post('/generate', async (req: Request, res: Response) => {
  try {
    const body = req.body as GeneratePlanRequest;
    const plan = await generatePlan(body);
    // 附带截图URL
    plan.screenshotUrl = body.screenshotUrl;
    res.json(plan);
  } catch (err) {
    console.error('generatePlan error', err);
    res.status(500).json({ error: 'Failed to generate plan' });
  }
});

/**
 * 上传执行结果
 */
router.post('/result', async (req: Request, res: Response) => {
  try {
    const body = req.body as UploadResultRequest;
    console.log('Received execution result:', JSON.stringify(body, null, 2));
    res.json({ success: true, message: 'Result saved' });
  } catch (err) {
    console.error('upload result error', err);
    res.status(500).json({ error: 'Failed to upload result' });
  }
});

export default router;
