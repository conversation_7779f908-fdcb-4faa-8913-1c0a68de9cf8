import { PlanRepository } from '../database/repositories/plan-repository-sqlite';
import { ExecutionRepository } from '../database/repositories/execution-repository-sqlite';
import { ActionRepository } from '../database/repositories/action-repository';
import { logger } from '../utils/logger';

export interface DashboardStats {
  totalPlans: number;
  totalExecutions: number;
  totalActions: number;
  avgSuccessRate: number;
  avgExecutionTime: number;
  recentActivity: {
    plansCreated: number;
    executionsCompleted: number;
    actionsPerformed: number;
  };
  statusDistribution: {
    pending: number;
    running: number;
    completed: number;
    failed: number;
  };
}

export interface ExecutionStats {
  date: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  avgDuration: number;
  successRate: number;
}

export interface PlanPerformance {
  planId: string;
  userInstruction: string;
  executionCount: number;
  successRate: number;
  avgDuration: number;
  lastExecuted: string;
}

export interface ActionFrequency {
  actionType: string;
  count: number;
  successRate: number;
  avgDuration: number;
}

export interface ErrorAnalysis {
  errorMessage: string;
  count: number;
  actionType: string;
  lastOccurrence: string;
  affectedPlans: string[];
}

export class AnalyticsService {
  private planRepo: PlanRepository;
  private executionRepo: ExecutionRepository;
  private actionRepo: ActionRepository;

  constructor() {
    this.planRepo = new PlanRepository();
    this.executionRepo = new ExecutionRepository();
    this.actionRepo = new ActionRepository();
  }

  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const [totalPlans, totalExecutions, totalActions] = await Promise.all([
        this.planRepo.count(),
        this.executionRepo.count(),
        this.actionRepo.count()
      ]);

      const executions = await this.executionRepo.findAll();
      const avgSuccessRate = executions.length > 0 
        ? executions.reduce((sum, exec) => sum + exec.success_rate, 0) / executions.length
        : 0;

      const avgExecutionTime = executions.length > 0
        ? executions.reduce((sum, exec) => sum + (exec.duration || 0), 0) / executions.length
        : 0;

      const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const [recentPlans, recentExecutions, recentActions] = await Promise.all([
        this.planRepo.findByDateRange(last24h, new Date()),
        this.executionRepo.findByDateRange(last24h, new Date()),
        this.actionRepo.findByDateRange(last24h, new Date())
      ]);

      const statusDistribution = {
        pending: 0,
        running: 0,
        completed: 0,
        failed: 0
      };

      executions.forEach(exec => {
        if (exec.status in statusDistribution) {
          statusDistribution[exec.status as keyof typeof statusDistribution]++;
        }
      });

      return {
        totalPlans,
        totalExecutions,
        totalActions,
        avgSuccessRate,
        avgExecutionTime,
        recentActivity: {
          plansCreated: recentPlans.length,
          executionsCompleted: recentExecutions.length,
          actionsPerformed: recentActions.length
        },
        statusDistribution
      };
    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  async getExecutionStats(timeRange: string, groupBy: string): Promise<ExecutionStats[]> {
    try {
      const startDate = this.parseTimeRange(timeRange);
      const executions = await this.executionRepo.findByDateRange(startDate, new Date());

      const grouped = this.groupExecutionsByTime(executions, groupBy);
      
      return Object.entries(grouped).map(([date, execs]) => ({
        date,
        totalExecutions: execs.length,
        successfulExecutions: execs.filter(e => e.status === 'completed').length,
        failedExecutions: execs.filter(e => e.status === 'failed').length,
        avgDuration: execs.reduce((sum, e) => sum + (e.duration || 0), 0) / execs.length,
        successRate: execs.length > 0 ? execs.reduce((sum, e) => sum + e.success_rate, 0) / execs.length : 0
      }));
    } catch (error) {
      logger.error('Error getting execution stats:', error);
      throw error;
    }
  }

  async getPlanPerformance(limit: number): Promise<PlanPerformance[]> {
    try {
      const executions = await this.executionRepo.findAll();
      const plans = await this.planRepo.findAll();
      
      const planMap = new Map(plans.map(p => [p.id, p]));
      const planStats = new Map<string, {
        executions: any[];
        successRate: number;
        avgDuration: number;
      }>();

      executions.forEach(exec => {
        if (!planStats.has(exec.plan_id)) {
          planStats.set(exec.plan_id, {
            executions: [],
            successRate: 0,
            avgDuration: 0
          });
        }
        planStats.get(exec.plan_id)!.executions.push(exec);
      });

      const performance: PlanPerformance[] = [];
      
      for (const [planId, stats] of planStats) {
        const plan = planMap.get(planId);
        if (!plan) continue;

        const successRate = stats.executions.reduce((sum, e) => sum + e.success_rate, 0) / stats.executions.length;
        const avgDuration = stats.executions.reduce((sum, e) => sum + (e.duration || 0), 0) / stats.executions.length;
        const lastExecuted = Math.max(...stats.executions.map(e => new Date(e.end_time || e.start_time).getTime()));

        performance.push({
          planId,
          userInstruction: plan.user_instruction,
          executionCount: stats.executions.length,
          successRate,
          avgDuration,
          lastExecuted: new Date(lastExecuted).toISOString()
        });
      }

      return performance
        .sort((a, b) => b.executionCount - a.executionCount)
        .slice(0, limit);
    } catch (error) {
      logger.error('Error getting plan performance:', error);
      throw error;
    }
  }

  async getActionFrequency(timeRange: string): Promise<ActionFrequency[]> {
    try {
      const startDate = this.parseTimeRange(timeRange);
      const actions = await this.actionRepo.findByDateRange(startDate, new Date());

      const frequency = new Map<string, {
        count: number;
        successes: number;
        totalDuration: number;
      }>();

      actions.forEach(action => {
        if (!frequency.has(action.type)) {
          frequency.set(action.type, {
            count: 0,
            successes: 0,
            totalDuration: 0
          });
        }
        
        const stats = frequency.get(action.type)!;
        stats.count++;
        stats.totalDuration += action.duration || 0;
        
        if (action.status === 'success') {
          stats.successes++;
        }
      });

      return Array.from(frequency.entries()).map(([actionType, stats]) => ({
        actionType,
        count: stats.count,
        successRate: stats.count > 0 ? (stats.successes / stats.count) * 100 : 0,
        avgDuration: stats.count > 0 ? stats.totalDuration / stats.count : 0
      })).sort((a, b) => b.count - a.count);
    } catch (error) {
      logger.error('Error getting action frequency:', error);
      throw error;
    }
  }

  async getErrorAnalysis(timeRange: string, limit: number): Promise<ErrorAnalysis[]> {
    try {
      const startDate = this.parseTimeRange(timeRange);
      const actions = await this.actionRepo.findByDateRange(startDate, new Date());
      
      const failedActions = actions.filter(a => a.status === 'failed' && a.error_message);
      const errorMap = new Map<string, {
        count: number;
        actionType: string;
        lastOccurrence: Date;
        planIds: Set<string>;
      }>();

      failedActions.forEach(action => {
        const message = action.error_message || 'Unknown error';
        
        if (!errorMap.has(message)) {
          errorMap.set(message, {
            count: 0,
            actionType: action.type,
            lastOccurrence: new Date(action.timestamp),
            planIds: new Set()
          });
        }
        
        const error = errorMap.get(message)!;
        error.count++;
        error.planIds.add(action.execution_id);
        
        if (new Date(action.timestamp) > error.lastOccurrence) {
          error.lastOccurrence = new Date(action.timestamp);
        }
      });

      const executions = await this.executionRepo.findAll();
      const executionPlanMap = new Map(executions.map(e => [e.id, e.plan_id]));

      return Array.from(errorMap.entries())
        .map(([errorMessage, error]) => ({
          errorMessage,
          count: error.count,
          actionType: error.actionType,
          lastOccurrence: error.lastOccurrence.toISOString(),
          affectedPlans: Array.from(error.planIds).map(execId => executionPlanMap.get(execId) || execId)
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);
    } catch (error) {
      logger.error('Error getting error analysis:', error);
      throw error;
    }
  }

  async getSuccessRateTrends(timeRange: string, groupBy: string): Promise<ExecutionStats[]> {
    return this.getExecutionStats(timeRange, groupBy);
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/^(\d+)([hdwmy])$/);
    
    if (!match) {
      throw new Error('Invalid time range format');
    }

    const [, amount, unit] = match;
    const value = parseInt(amount);

    switch (unit) {
      case 'h':
        return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd':
        return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w':
        return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm':
        return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      case 'y':
        return new Date(now.getTime() - value * 365 * 24 * 60 * 60 * 1000);
      default:
        throw new Error('Invalid time unit');
    }
  }

  private groupExecutionsByTime(executions: any[], groupBy: string): Record<string, any[]> {
    const grouped: Record<string, any[]> = {};
    
    executions.forEach(exec => {
      const date = new Date(exec.start_time);
      let key: string;
      
      switch (groupBy) {
        case 'hour':
          key = date.toISOString().slice(0, 13);
          break;
        case 'day':
          key = date.toISOString().slice(0, 10);
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().slice(0, 10);
          break;
        case 'month':
          key = date.toISOString().slice(0, 7);
          break;
        default:
          key = date.toISOString().slice(0, 10);
      }
      
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(exec);
    });
    
    return grouped;
  }
}