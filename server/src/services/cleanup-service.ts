import { PlanRepository } from '../database/repositories/plan-repository-sqlite';
import { ExecutionRepository } from '../database/repositories/execution-repository-sqlite';
import { ActionRepository } from '../database/repositories/action-repository';
import { logger } from '../utils/logger';
import { promises as fs } from 'fs';
import { join } from 'path';

export interface CleanupConfig {
  retentionPeriodDays: number;
  maxRecordsPerTable: number;
  cleanupScreenshots: boolean;
  cleanupInterval: number; // in milliseconds
}

export interface CleanupResult {
  plansDeleted: number;
  executionsDeleted: number;
  actionsDeleted: number;
  screenshotsDeleted: number;
  bytesFreed: number;
  duration: number;
}

export class CleanupService {
  private planRepo: PlanRepository;
  private executionRepo: ExecutionRepository;
  private actionRepo: ActionRepository;
  private config: CleanupConfig;
  private isRunning: boolean = false;
  private intervalId?: NodeJS.Timeout;

  constructor(config: CleanupConfig) {
    this.planRepo = new PlanRepository();
    this.executionRepo = new ExecutionRepository();
    this.actionRepo = new ActionRepository();
    this.config = config;
  }

  async performCleanup(): Promise<CleanupResult> {
    if (this.isRunning) {
      logger.warn('Cleanup already running, skipping...');
      return {
        plansDeleted: 0,
        executionsDeleted: 0,
        actionsDeleted: 0,
        screenshotsDeleted: 0,
        bytesFreed: 0,
        duration: 0
      };
    }

    this.isRunning = true;
    const startTime = Date.now();
    
    try {
      logger.info('Starting cleanup process...');
      
      const result: CleanupResult = {
        plansDeleted: 0,
        executionsDeleted: 0,
        actionsDeleted: 0,
        screenshotsDeleted: 0,
        bytesFreed: 0,
        duration: 0
      };

      // Calculate cutoff date for retention
      const cutoffDate = new Date(Date.now() - this.config.retentionPeriodDays * 24 * 60 * 60 * 1000);
      
      // Clean up old records
      await this.cleanupOldRecords(cutoffDate, result);
      
      // Clean up excess records (keep only max records per table)
      await this.cleanupExcessRecords(result);
      
      // Clean up orphaned screenshots
      if (this.config.cleanupScreenshots) {
        await this.cleanupOrphanedScreenshots(result);
      }

      result.duration = Date.now() - startTime;
      
      logger.info('Cleanup completed:', {
        plansDeleted: result.plansDeleted,
        executionsDeleted: result.executionsDeleted,
        actionsDeleted: result.actionsDeleted,
        screenshotsDeleted: result.screenshotsDeleted,
        bytesFreed: result.bytesFreed,
        duration: result.duration
      });

      return result;
    } catch (error) {
      logger.error('Cleanup failed:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  private async cleanupOldRecords(cutoffDate: Date, result: CleanupResult): Promise<void> {
    logger.info(`Cleaning up records older than ${cutoffDate.toISOString()}`);

    // Get old executions first (needed for cascade cleanup)
    const oldExecutions = await this.executionRepo.findByDateRange(new Date(0), cutoffDate);
    const oldExecutionIds = oldExecutions.map(e => e.id);

    // Delete old actions
    if (oldExecutionIds.length > 0) {
      for (const executionId of oldExecutionIds) {
        const deletedActions = await this.actionRepo.deleteByExecutionId(executionId);
        result.actionsDeleted += deletedActions;
      }
    }

    // Delete old executions
    const deletedExecutions = await this.executionRepo.deleteByDateRange(new Date(0), cutoffDate);
    result.executionsDeleted += deletedExecutions;

    // Delete old plans (only if they have no recent executions)
    const oldPlans = await this.planRepo.findByDateRange(new Date(0), cutoffDate);
    for (const plan of oldPlans) {
      const recentExecutions = await this.executionRepo.findByPlanId(plan.id);
      const hasRecentExecutions = recentExecutions.some(e => new Date(e.start_time) > cutoffDate);
      
      if (!hasRecentExecutions) {
        await this.planRepo.delete(plan.id);
        result.plansDeleted++;
      }
    }
  }

  private async cleanupExcessRecords(result: CleanupResult): Promise<void> {
    logger.info(`Cleaning up excess records (keeping max ${this.config.maxRecordsPerTable} per table)`);

    // Clean up excess actions
    const totalActions = await this.actionRepo.count();
    if (totalActions > this.config.maxRecordsPerTable) {
      const excessActions = totalActions - this.config.maxRecordsPerTable;
      const deletedActions = await this.actionRepo.deleteOldest(excessActions);
      result.actionsDeleted += deletedActions;
    }

    // Clean up excess executions
    const totalExecutions = await this.executionRepo.count();
    if (totalExecutions > this.config.maxRecordsPerTable) {
      const excessExecutions = totalExecutions - this.config.maxRecordsPerTable;
      const deletedExecutions = await this.executionRepo.deleteOldest(excessExecutions);
      result.executionsDeleted += deletedExecutions;
    }

    // Clean up excess plans
    const totalPlans = await this.planRepo.count();
    if (totalPlans > this.config.maxRecordsPerTable) {
      const excessPlans = totalPlans - this.config.maxRecordsPerTable;
      const deletedPlans = await this.planRepo.deleteOldest(excessPlans);
      result.plansDeleted += deletedPlans;
    }
  }

  private async cleanupOrphanedScreenshots(result: CleanupResult): Promise<void> {
    logger.info('Cleaning up orphaned screenshots...');

    const uploadsDir = join(process.cwd(), 'uploads');
    
    try {
      // Get all screenshot files
      const files = await fs.readdir(uploadsDir);
      const screenshotFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));

      // Get all screenshot paths from database
      const actions = await this.actionRepo.findAll();
      const dbScreenshots = new Set(
        actions
          .filter(a => a.screenshot_path)
          .map(a => a.screenshot_path!.split('/').pop())
      );

      // Delete orphaned screenshots
      for (const file of screenshotFiles) {
        if (!dbScreenshots.has(file)) {
          const filePath = join(uploadsDir, file);
          const stats = await fs.stat(filePath);
          await fs.unlink(filePath);
          
          result.screenshotsDeleted++;
          result.bytesFreed += stats.size;
        }
      }
    } catch (error) {
      logger.error('Error cleaning up screenshots:', error);
    }
  }

  startScheduledCleanup(): void {
    if (this.intervalId) {
      logger.warn('Scheduled cleanup already running');
      return;
    }

    logger.info(`Starting scheduled cleanup every ${this.config.cleanupInterval}ms`);
    
    this.intervalId = setInterval(async () => {
      try {
        await this.performCleanup();
      } catch (error) {
        logger.error('Scheduled cleanup failed:', error);
      }
    }, this.config.cleanupInterval);
  }

  stopScheduledCleanup(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
      logger.info('Scheduled cleanup stopped');
    }
  }

  async getCleanupPreview(): Promise<{
    oldRecordsCount: {
      plans: number;
      executions: number;
      actions: number;
    };
    excessRecordsCount: {
      plans: number;
      executions: number;
      actions: number;
    };
    orphanedScreenshots: number;
    estimatedBytesToFree: number;
  }> {
    const cutoffDate = new Date(Date.now() - this.config.retentionPeriodDays * 24 * 60 * 60 * 1000);
    
    const oldExecutions = await this.executionRepo.findByDateRange(new Date(0), cutoffDate);
    const oldExecutionIds = oldExecutions.map(e => e.id);
    
    let oldActionsCount = 0;
    for (const executionId of oldExecutionIds) {
      const actions = await this.actionRepo.findByExecutionId(executionId);
      oldActionsCount += actions.length;
    }

    const oldPlans = await this.planRepo.findByDateRange(new Date(0), cutoffDate);
    let oldPlansCount = 0;
    for (const plan of oldPlans) {
      const recentExecutions = await this.executionRepo.findByPlanId(plan.id);
      const hasRecentExecutions = recentExecutions.some(e => new Date(e.start_time) > cutoffDate);
      if (!hasRecentExecutions) {
        oldPlansCount++;
      }
    }

    // Calculate excess records
    const [totalPlans, totalExecutions, totalActions] = await Promise.all([
      this.planRepo.count(),
      this.executionRepo.count(),
      this.actionRepo.count()
    ]);

    const excessPlans = Math.max(0, totalPlans - this.config.maxRecordsPerTable);
    const excessExecutions = Math.max(0, totalExecutions - this.config.maxRecordsPerTable);
    const excessActions = Math.max(0, totalActions - this.config.maxRecordsPerTable);

    // Calculate orphaned screenshots
    let orphanedScreenshots = 0;
    let estimatedBytesToFree = 0;
    
    try {
      const uploadsDir = join(process.cwd(), 'uploads');
      const files = await fs.readdir(uploadsDir);
      const screenshotFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));

      const actions = await this.actionRepo.findAll();
      const dbScreenshots = new Set(
        actions
          .filter(a => a.screenshot_path)
          .map(a => a.screenshot_path!.split('/').pop())
      );

      for (const file of screenshotFiles) {
        if (!dbScreenshots.has(file)) {
          const stats = await fs.stat(join(uploadsDir, file));
          orphanedScreenshots++;
          estimatedBytesToFree += stats.size;
        }
      }
    } catch (error) {
      logger.error('Error calculating orphaned screenshots:', error);
    }

    return {
      oldRecordsCount: {
        plans: oldPlansCount,
        executions: oldExecutions.length,
        actions: oldActionsCount
      },
      excessRecordsCount: {
        plans: excessPlans,
        executions: excessExecutions,
        actions: excessActions
      },
      orphanedScreenshots,
      estimatedBytesToFree
    };
  }
}