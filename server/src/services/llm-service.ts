/**
 * Simplified LLM service with single provider configuration
 */

import fetch from 'node-fetch';
import type { ConversationMessage } from '../types';

export interface LLMConfig {
  // LLM Configuration
  llm: {
    apiKey: string;
    endpoint: string;
    model: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
  };
  // VLM Configuration (Vision Language Model)
  vlm: {
    apiKey: string;
    endpoint: string;
    model: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
  };
}

export interface LLMResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  responseTime: number;
}

export interface LLMRequest {
  messages: ConversationMessage[];
  instruction: string;
  domTreeText: string;
  temperature?: number;
  maxTokens?: number;
}

export interface VLMRequest {
  messages: ConversationMessage[];
  instruction: string;
  imageBase64?: string;
  temperature?: number;
  maxTokens?: number;
}

export class LLMService {
  private config: LLMConfig;

  constructor(config: LLMConfig) {
    this.config = config;
    this.validateConfig();
  }

  /**
   * Call LLM for text-based planning
   */
  async callLLM(request: LLMRequest): Promise<LLMResponse> {
    const startTime = Date.now();
    
    const body = {
      model: this.config.llm.model,
      messages: [
        { role: 'system', content: this.buildLLMSystemPrompt(request.domTreeText) },
        ...request.messages,
        { role: 'user', content: request.instruction },
      ],
      temperature: request.temperature || this.config.llm.temperature,
      max_tokens: request.maxTokens || this.config.llm.maxTokens,
      stream: false,
    };

    const response = await fetch(this.config.llm.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.llm.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(this.config.llm.timeout),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`LLM API error: ${response.status} ${errorText}`);
    }

    const data = await response.json() as any;
    const content = data.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content in LLM response');
    }

    const usage = data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
    const responseTime = Date.now() - startTime;

    return {
      content,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      responseTime,
    };
  }

  /**
   * Call VLM for vision-based analysis
   */
  async callVLM(request: VLMRequest): Promise<LLMResponse> {
    const startTime = Date.now();
    
    const messages = [
      { role: 'system', content: this.buildVLMSystemPrompt() },
      ...request.messages,
    ];

    // Add user message with optional image
    const userMessage: any = {
      role: 'user',
      content: []
    };

    // Add text content
    userMessage.content.push({
      type: 'text',
      text: request.instruction
    });

    // Add image content if provided
    if (request.imageBase64) {
      userMessage.content.push({
        type: 'image_url',
        image_url: {
          url: `data:image/png;base64,${request.imageBase64}`
        }
      });
    }

    messages.push(userMessage);

    const body = {
      model: this.config.vlm.model,
      messages,
      temperature: request.temperature || this.config.vlm.temperature,
      max_tokens: request.maxTokens || this.config.vlm.maxTokens,
      stream: false,
    };

    const response = await fetch(this.config.vlm.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.vlm.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(this.config.vlm.timeout),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`VLM API error: ${response.status} ${errorText}`);
    }

    const data = await response.json() as any;
    const content = data.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content in VLM response');
    }

    const usage = data.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
    const responseTime = Date.now() - startTime;

    return {
      content,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      responseTime,
    };
  }

  /**
   * Build system prompt for LLM (text-based planning)
   */
  private buildLLMSystemPrompt(domTreeText: string): string {
    return `你是一个网页操作助手，负责根据用户的自然语言指令，生成一系列网页操作步骤。

请遵循以下规则生成操作计划：
1. 识别用户的意图，拆解为多步操作。
2. 操作类型包括：Locate（定位元素）、Tap（点击）、Input（输入文本）、Scroll（滚动）、KeyboardPress（快捷键）、Drag（拖拽）、Finished（完成）。
3. 每步操作需包含：
   - type：操作类型
   - thought：该步的思考
   - param：操作参数
   - locate：元素定位信息，包括prompt描述和bbox坐标
4. 输出格式为JSON数组，示例：
[
  {
    "type": "Locate",
    "thought": "定位登录按钮",
    "param": {},
    "locate": {
      "prompt": "登录按钮",
      "bbox": {"x":100,"y":200,"width":50,"height":20}
    }
  },
  {
    "type": "Tap",
    "thought": "点击登录按钮",
    "param": "点击登录",
    "locate": {
      "prompt": "登录按钮",
      "bbox": {"x":100,"y":200,"width":50,"height":20}
    }
  }
]
5. 只输出JSON，不要输出其他内容。

以下是当前页面的DOM树结构（JSON格式）：
${domTreeText}`;
  }

  /**
   * Build system prompt for VLM (vision-based analysis)
   */
  private buildVLMSystemPrompt(): string {
    return `你是一个网页视觉分析助手，负责分析网页截图并生成操作步骤。

请遵循以下规则：
1. 仔细观察提供的网页截图
2. 根据用户指令识别需要操作的UI元素
3. 估算元素的位置坐标（基于图片的像素坐标）
4. 生成详细的操作计划

输出格式为JSON数组，包含操作步骤：
[
  {
    "type": "Locate",
    "thought": "在截图中定位到目标元素",
    "param": {},
    "locate": {
      "prompt": "元素描述",
      "bbox": {"x":估算的x坐标,"y":估算的y坐标,"width":估算宽度,"height":估算高度}
    }
  }
]

只输出JSON，不要输出其他内容。`;
  }

  /**
   * Validate configuration
   */
  private validateConfig(): void {
    if (!this.config.llm.apiKey) {
      throw new Error('LLM API key is required');
    }
    if (!this.config.llm.endpoint) {
      throw new Error('LLM endpoint is required');
    }
    if (!this.config.vlm.apiKey) {
      throw new Error('VLM API key is required');
    }
    if (!this.config.vlm.endpoint) {
      throw new Error('VLM endpoint is required');
    }
  }

  /**
   * Test LLM connection
   */
  async testLLM(): Promise<boolean> {
    try {
      const testRequest: LLMRequest = {
        messages: [],
        instruction: '测试连接',
        domTreeText: '',
        temperature: 0.1,
        maxTokens: 10,
      };

      await this.callLLM(testRequest);
      return true;
    } catch (error) {
      console.error('LLM test failed:', error);
      return false;
    }
  }

  /**
   * Test VLM connection
   */
  async testVLM(): Promise<boolean> {
    try {
      const testRequest: VLMRequest = {
        messages: [],
        instruction: '测试连接',
        temperature: 0.1,
        maxTokens: 10,
      };

      await this.callVLM(testRequest);
      return true;
    } catch (error) {
      console.error('VLM test failed:', error);
      return false;
    }
  }

  /**
   * Get configuration summary
   */
  getConfigSummary() {
    return {
      llm: {
        model: this.config.llm.model,
        endpoint: this.config.llm.endpoint,
        maxTokens: this.config.llm.maxTokens,
        temperature: this.config.llm.temperature,
        timeout: this.config.llm.timeout,
      },
      vlm: {
        model: this.config.vlm.model,
        endpoint: this.config.vlm.endpoint,
        maxTokens: this.config.vlm.maxTokens,
        temperature: this.config.vlm.temperature,
        timeout: this.config.vlm.timeout,
      }
    };
  }
}

/**
 * Load LLM configuration from environment variables
 */
export function loadLLMConfig(): LLMConfig {
  return {
    llm: {
      apiKey: process.env.LLM_API_KEY || process.env.OPENAI_API_KEY || '',
      endpoint: process.env.LLM_ENDPOINT || process.env.OPENAI_API_URL || 'https://openrouter.ai/api/v1/chat/completions',
      model: process.env.LLM_MODEL || process.env.OPENAI_MODEL || 'openrouter/optimus-alpha',
      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '8192'),
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.2'),
      timeout: parseInt(process.env.LLM_TIMEOUT || '30000'),
    },
    vlm: {
      apiKey: process.env.VLM_API_KEY || process.env.OPENAI_API_KEY || '',
      endpoint: process.env.VLM_ENDPOINT || process.env.OPENAI_API_URL || 'https://openrouter.ai/api/v1/chat/completions',
      model: process.env.VLM_MODEL || 'gpt-4-vision-preview',
      maxTokens: parseInt(process.env.VLM_MAX_TOKENS || '4096'),
      temperature: parseFloat(process.env.VLM_TEMPERATURE || '0.2'),
      timeout: parseInt(process.env.VLM_TIMEOUT || '30000'),
    },
  };
}

/**
 * Create and initialize LLM service
 */
export function createLLMService(): LLMService {
  const config = loadLLMConfig();
  const service = new LLMService(config);
  
  console.log('🤖 LLM Service Configuration:');
  console.log(`   LLM Model: ${config.llm.model}`);
  console.log(`   VLM Model: ${config.vlm.model}`);
  console.log(`   LLM Max Tokens: ${config.llm.maxTokens}`);
  console.log(`   VLM Max Tokens: ${config.vlm.maxTokens}`);
  
  return service;
}