import fetch from 'node-fetch';
import type { ConversationMessage } from '../types';

import fs from 'node:fs';
import path from 'node:path';
import { SocksProxyAgent } from 'socks-proxy-agent';

const uiTarsPlanningPrompt = `
你是一个网页操作助手，负责根据用户的自然语言指令，生成一系列网页操作步骤。

请遵循以下规则生成操作计划：
1. 识别用户的意图，拆解为多步操作。
2. 操作类型包括：Locate（定位元素）、Tap（点击）、Input（输入文本）、Scroll（滚动）、KeyboardPress（快捷键）、Drag（拖拽）、Finished（完成）。
3. 每步操作需包含：
   - type：操作类型
   - thought：该步的思考
   - param：操作参数
   - locate：元素定位信息，包括prompt描述和bbox坐标
4. 输出格式为JSON数组，示例：
[
  {
    "type": "Locate",
    "thought": "定位登录按钮",
    "param": {},
    "locate": {
      "prompt": "登录按钮",
      "bbox": {"x":100,"y":200,"width":50,"height":20}
    }
  },
  {
    "type": "Tap",
    "thought": "点击登录按钮",
    "param": "点击登录",
    "locate": {
      "prompt": "登录按钮",
      "bbox": {"x":100,"y":200,"width":50,"height":20}
    }
  }
]
5. 只输出JSON，不要输出其他内容。
`;

export async function callLargeModel(
  messages: ConversationMessage[],
  instruction: string,
  domId?: string,
): Promise<string> {
  const apiKey = process.env.OPENAI_API_KEY;
  const apiUrl =
    process.env.OPENAI_API_URL ||
    'https://openrouter.ai/api/v1/chat/completions';
  const model = process.env.OPENAI_MODEL || 'openrouter/optimus-alpha';

  if (!apiKey) throw new Error('Missing OPENAI_API_KEY');

  let domTreeText = '';
  if (domId) {
    try {
      const domFile = path.join(__dirname, '../../../uploads', `${domId}.json`);
      domTreeText = fs.readFileSync(domFile, 'utf-8');
    } catch {
      domTreeText = '';
    }
  }

  const fullSystemPrompt = `${uiTarsPlanningPrompt}\n\n以下是当前页面的DOM树结构（JSON格式）：\n${domTreeText}`;

  const fullMessages = [
    { role: 'system', content: fullSystemPrompt },
    ...messages,
    { role: 'user', content: [{ type: 'text', text: instruction }] },
  ];

  const body = {
    model,
    messages: fullMessages,
    temperature: 0.2,
    max_tokens: 8192,
    stream: false,
  };

  const jsonbody = JSON.stringify(body);

  const resp = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: jsonbody,
  });

  if (!resp.ok) {
    const text = await resp.text();
    throw new Error(`OpenAI API error: ${resp.status} ${text}`);
  }

  const data = (await resp.json()) as any;

  const content = data.choices?.[0]?.message?.content;
  if (!content) throw new Error('No content in OpenAI response');

  return content;
}
