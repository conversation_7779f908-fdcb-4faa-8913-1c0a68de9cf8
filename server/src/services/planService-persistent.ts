/**
 * Enhanced plan service with database persistence
 * Integrates the existing plan generation with data persistence
 */

import { v4 as uuidv4 } from 'uuid';
import type {
  GeneratePlanRequest,
  GeneratePlanResponse,
  PlanningAction,
  UploadResultRequest,
  StepResult,
} from '../types';
import { createLLMService } from './llm-service';
import { RepositoryFactory } from '../database/repositories';
import fs from 'node:fs';
import path from 'node:path';
import { 
  CreatePlanRequest, 
  CreateSessionRequest, 
  CreateExecutionRequest, 
  CreateExecutionStepRequest,
  CreateProviderMetricRequest,
  CreateDomUploadRequest
} from '../database/models';
import { logger } from '../utils/logger';

// Initialize LLM service
const llmService = createLLMService();

/**
 * Load DOM tree from file
 */
async function loadDomTree(domId: string): Promise<string> {
  try {
    const domFile = path.join(__dirname, '../../../uploads', `${domId}.json`);
    if (fs.existsSync(domFile)) {
      const data = fs.readFileSync(domFile, 'utf-8');
      const domData = JSON.parse(data);
      return JSON.stringify(domData.domTree || domData);
    }
    return '';
  } catch (error) {
    console.warn(`⚠️ Failed to load DOM tree ${domId}:`, error);
    return '';
  }
}

// Get repository instances
const sessionRepo = RepositoryFactory.getSessionRepository();
const planRepo = RepositoryFactory.getPlanRepository();
const executionRepo = RepositoryFactory.getExecutionRepository();
const executionStepRepo = RepositoryFactory.getExecutionStepRepository();

interface PlanGenerationResult {
  plan: GeneratePlanResponse;
  metadata: {
    provider: string;
    model: string;
    cost: number;
    responseTime: number;
    tokenUsage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
    retryCount: number;
    validationPassed: boolean;
  };
}

/**
 * Generate execution plan with database persistence
 */
export async function generatePlan(
  req: GeneratePlanRequest
): Promise<GeneratePlanResponse> {
  const result = await generatePlanWithMetadata(req);
  return result.plan;
}

/**
 * Generate execution plan with detailed metadata and persistence
 */
export async function generatePlanWithMetadata(
  req: GeneratePlanRequest
): Promise<PlanGenerationResult> {
  const { userInstruction, conversationHistory, size, domId, sessionId, screenshotUrl } = req;
  const startTime = Date.now();
  let retryCount = 0;
  let lastError: Error | null = null;

  // Validate input
  validatePlanRequest(req);

  // Ensure session exists
  const session = await ensureSession(sessionId || 'default-session', {
    screen_width: size.width,
    screen_height: size.height,
    user_agent: 'Unknown'
  });

  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    try {
      logger.info('Generating plan', {
        attempt: retryCount + 1,
        maxRetries,
        userInstruction: userInstruction.substring(0, 100) + '...',
        sessionId: session.session_id
      });
      
      // Load DOM tree if domId is provided
      const domTreeText = domId ? await loadDomTree(domId) : '';
      
      // Call LLM service
      const llmResponse = await llmService.callLLM({
        messages: conversationHistory,
        instruction: userInstruction,
        domTreeText,
      });

      // Parse LLM response
      const { actions, actionSummary } = await parseLLMResponse(llmResponse.content);

      // Create plan
      const plan: GeneratePlanResponse = {
        planId: uuidv4(),
        actions,
        action_summary: actionSummary,
        screenshotUrl,
      };

      // Validate plan
      const validationResult = validatePlan(plan);
      if (!validationResult.valid) {
        throw new Error(`Plan validation failed: ${validationResult.error}`);
      }

      const totalTime = Date.now() - startTime;
      
      // Save plan to database
      await savePlanToDatabase(plan, session, llmResponse, {
        userInstruction,
        domId,
        retryCount,
        totalTime,
        validationPassed: true
      });

      logger.info('Plan generated successfully', {
        planId: plan.planId,
        actionsCount: actions.length,
        totalTime,
        cost: llmResponse.cost,
        tokens: llmResponse.usage.totalTokens,
        provider: llmResponse.provider
      });

      return {
        plan,
        metadata: {
          provider: llmResponse.provider,
          model: llmResponse.model,
          cost: llmResponse.cost,
          responseTime: llmResponse.responseTime,
          tokenUsage: llmResponse.usage,
          retryCount,
          validationPassed: true,
        },
      };

    } catch (error) {
      lastError = error as Error;
      retryCount++;
      
      logger.error('Plan generation attempt failed', {
        attempt: retryCount,
        maxRetries,
        error: error instanceof Error ? error.message : 'Unknown error',
        sessionId: session.session_id
      });
      
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
        logger.info('Retrying plan generation', { delay, nextAttempt: retryCount + 1 });
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All retries failed - still save failure metrics
  await saveFailedPlanGeneration(session, {
    userInstruction,
    retryCount,
    error: lastError?.message || 'Unknown error'
  });

  throw new Error(`Plan generation failed after ${maxRetries} attempts. Last error: ${lastError?.message}`);
}

/**
 * Save execution results to database
 */
export async function saveExecutionResults(
  req: UploadResultRequest
): Promise<void> {
  const { planId, sessionId, results, overallStatus, errorMessage, failedStepId } = req;

  try {
    // Get plan and session
    const plan = await planRepo.findByPlanId(planId);
    const session = await sessionRepo.findBySessionId(sessionId);

    if (!plan || !session) {
      throw new Error('Plan or session not found');
    }

    // Calculate timing and statistics
    const totalSteps = results.length;
    const successfulSteps = results.filter(r => r.status === 'success').length;
    const failedSteps = results.filter(r => r.status === 'failed').length;
    const executionTimes = results.map(r => r.executionTime || 0);
    const totalExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0);

    // Create execution record
    const executionData: CreateExecutionRequest = {
      execution_id: uuidv4(),
      plan_id: plan.id,
      session_id: session.id,
      overall_status: overallStatus,
      total_steps: totalSteps,
      successful_steps: successfulSteps,
      failed_steps: failedSteps,
      error_message: errorMessage,
      failed_step_id: failedStepId,
      failed_step_number: failedStepId ? 
        results.findIndex(r => r.actionId === failedStepId) + 1 : undefined,
      execution_start_time: new Date(),
      execution_end_time: new Date(Date.now() + totalExecutionTime),
      total_execution_time_ms: totalExecutionTime,
      result_file_path: `uploads/results/result-${planId}.json`
    };

    const executionResult = await executionRepo.create(executionData);
    if (!executionResult.success) {
      throw new Error(`Failed to save execution: ${executionResult.error}`);
    }

    // Save individual step results
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const stepData: CreateExecutionStepRequest = {
        execution_id: executionResult.data!.id,
        plan_id: plan.id,
        step_number: result.step,
        action_id: result.actionId,
        action_type: getActionType(result.actionId, plan.actions_json),
        status: result.status,
        log_message: result.log,
        error_message: result.status === 'failed' ? result.log : undefined,
        screenshot_base64: result.screenshot,
        execution_time_ms: result.executionTime || 0,
        retry_count: 0
      };

      await executionStepRepo.create(stepData);
    }

    // Update session activity
    await sessionRepo.updateSessionActivity(sessionId);

    logger.info('Execution results saved successfully', {
      planId,
      sessionId,
      totalSteps,
      successfulSteps,
      failedSteps,
      overallStatus
    });

  } catch (error) {
    logger.error('Error saving execution results', {
      planId,
      sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Save DOM upload metadata
 */
export async function saveDomUpload(
  domId: string,
  sessionId: string,
  filePath: string,
  metadata: {
    fileSize?: number;
    nodeCount?: number;
    formCount?: number;
    buttonCount?: number;
    inputCount?: number;
    linkCount?: number;
    imageCount?: number;
  }
): Promise<void> {
  try {
    const session = await sessionRepo.findBySessionId(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const domData: CreateDomUploadRequest = {
      dom_id: domId,
      session_id: session.id,
      file_path: filePath,
      file_size: metadata.fileSize,
      node_count: metadata.nodeCount,
      form_count: metadata.formCount || 0,
      button_count: metadata.buttonCount || 0,
      input_count: metadata.inputCount || 0,
      link_count: metadata.linkCount || 0,
      image_count: metadata.imageCount || 0
    };

    const result = await RepositoryFactory.getDomUploadRepository().create(domData);
    if (!result.success) {
      throw new Error(`Failed to save DOM upload: ${result.error}`);
    }

    logger.info('DOM upload saved successfully', {
      domId,
      sessionId,
      fileSize: metadata.fileSize,
      nodeCount: metadata.nodeCount
    });

  } catch (error) {
    logger.error('Error saving DOM upload', {
      domId,
      sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Get execution history for a session
 */
export async function getExecutionHistory(
  sessionId: string,
  limit: number = 50
): Promise<any[]> {
  try {
    const session = await sessionRepo.findBySessionId(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const executions = await executionRepo.findBySessionId(session.id, {
      limit,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });

    return executions.rows;
  } catch (error) {
    logger.error('Error getting execution history', {
      sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Get plan analytics
 */
export async function getPlanAnalytics(timeframe: '24h' | '7d' | '30d' | '90d' = '24h'): Promise<any> {
  try {
    const planStats = await planRepo.getPlanStats(timeframe);
    const executionStats = await executionRepo.getExecutionStats(timeframe);
    const sessionStats = await sessionRepo.getSessionStats(timeframe);

    return {
      plans: planStats,
      executions: executionStats,
      sessions: sessionStats
    };
  } catch (error) {
    logger.error('Error getting plan analytics', {
      timeframe,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

// Helper functions

/**
 * Ensure session exists or create it
 */
async function ensureSession(sessionId: string, metadata: {
  screen_width?: number;
  screen_height?: number;
  user_agent?: string;
}): Promise<any> {
  let session = await sessionRepo.findBySessionId(sessionId);
  
  if (!session) {
    const sessionData: CreateSessionRequest = {
      session_id: sessionId,
      screen_width: metadata.screen_width,
      screen_height: metadata.screen_height,
      user_agent: metadata.user_agent
    };

    const result = await sessionRepo.create(sessionData);
    if (!result.success) {
      throw new Error(`Failed to create session: ${result.error}`);
    }
    session = result.data;
  }

  return session;
}

/**
 * Save plan to database
 */
async function savePlanToDatabase(
  plan: GeneratePlanResponse,
  session: any,
  llmResponse: any,
  metadata: {
    userInstruction: string;
    domId?: string;
    retryCount: number;
    totalTime: number;
    validationPassed: boolean;
  }
): Promise<void> {
  const planData: CreatePlanRequest = {
    plan_id: plan.planId,
    session_id: session.id,
    user_instruction: metadata.userInstruction,
    action_summary: plan.action_summary,
    actions_json: plan.actions,
    action_count: plan.actions.length,
    provider_used: llmResponse.provider,
    model_used: llmResponse.model,
    prompt_tokens: llmResponse.usage.promptTokens,
    completion_tokens: llmResponse.usage.completionTokens,
    total_tokens: llmResponse.usage.totalTokens,
    cost_usd: llmResponse.cost,
    generation_time_ms: metadata.totalTime,
    retry_count: metadata.retryCount,
    validation_passed: metadata.validationPassed,
    fallback_used: llmResponse.fallbackUsed || false,
    screenshot_url: plan.screenshotUrl
  };

  const result = await planRepo.create(planData);
  if (!result.success) {
    throw new Error(`Failed to save plan: ${result.error}`);
  }

  // Save provider metrics
  const providerMetric: CreateProviderMetricRequest = {
    provider_name: llmResponse.provider,
    model_name: llmResponse.model,
    request_type: 'plan_generation',
    plan_id: result.data!.id,
    response_time_ms: llmResponse.responseTime,
    prompt_tokens: llmResponse.usage.promptTokens,
    completion_tokens: llmResponse.usage.completionTokens,
    total_tokens: llmResponse.usage.totalTokens,
    cost_usd: llmResponse.cost,
    success: true,
    provider_health_score: 1.0
  };

  // Save provider metrics (assuming we have a provider metrics repository)
  // await providerMetricsRepo.create(providerMetric);
}

/**
 * Save failed plan generation
 */
async function saveFailedPlanGeneration(
  session: any,
  metadata: {
    userInstruction: string;
    retryCount: number;
    error: string;
  }
): Promise<void> {
  // Log the failure for analytics
  logger.error('Plan generation failed completely', {
    sessionId: session.session_id,
    userInstruction: metadata.userInstruction.substring(0, 100) + '...',
    retryCount: metadata.retryCount,
    error: metadata.error
  });

  // Could save failed attempts to a separate table for analysis
  // This would help identify common failure patterns
}

/**
 * Get action type from action ID
 */
function getActionType(actionId: string, actions: PlanningAction[]): string {
  const action = actions.find(a => a.id === actionId);
  return action?.type || 'Unknown';
}

// Re-export validation functions from the original service
export { validatePlanRequest, parseLLMResponse, validatePlan } from './planService-enhanced';

// Re-export LLM management functions
export {
  getLLMStats,
  checkLLMHealth,
  setProviderEnabled,
  getAvailableProviders,
  getHealthyProviders
} from './planService-enhanced';

/**
 * Validate plan generation request
 */
function validatePlanRequest(req: GeneratePlanRequest): void {
  if (!req.userInstruction || req.userInstruction.trim().length === 0) {
    throw new Error('User instruction is required');
  }

  if (req.userInstruction.length > 1000) {
    throw new Error('User instruction too long (max 1000 characters)');
  }

  if (!req.conversationHistory || !Array.isArray(req.conversationHistory)) {
    throw new Error('Conversation history must be an array');
  }

  if (!req.size || typeof req.size.width !== 'number' || typeof req.size.height !== 'number') {
    throw new Error('Valid size with width and height is required');
  }

  if (req.size.width <= 0 || req.size.height <= 0) {
    throw new Error('Size dimensions must be positive');
  }

  if (req.size.width > 8192 || req.size.height > 8192) {
    throw new Error('Size dimensions too large (max 8192)');
  }
}

/**
 * Parse LLM response and extract actions
 */
async function parseLLMResponse(content: string): Promise<{
  actions: PlanningAction[];
  actionSummary: string;
}> {
  let actions: PlanningAction[] = [];
  let actionSummary = content;

  try {
    // Try to parse as JSON
    const parsed = JSON.parse(content);
    
    if (Array.isArray(parsed)) {
      // Direct array of actions
      actions = parsed.map((item: any, index: number) => ({
        id: uuidv4(),
        type: item.type || 'Unknown',
        locate: item.locate || null,
        param: item.param || null,
        thought: item.thought || `Action ${index + 1}`,
      }));
      actionSummary = `Generated ${actions.length} actions successfully`;
    } else if (parsed.actions && Array.isArray(parsed.actions)) {
      // Wrapped in object with actions property
      actions = parsed.actions.map((item: any, index: number) => ({
        id: uuidv4(),
        type: item.type || 'Unknown',
        locate: item.locate || null,
        param: item.param || null,
        thought: item.thought || `Action ${index + 1}`,
      }));
      actionSummary = parsed.summary || `Generated ${actions.length} actions successfully`;
    } else {
      throw new Error('Invalid response format');
    }
  } catch (parseError) {
    // If JSON parsing fails, try to extract JSON from text
    const jsonMatch = content.match(/\[[\s\S]*\]/);
    
    if (jsonMatch) {
      try {
        const parsed = JSON.parse(jsonMatch[0]);
        actions = parsed.map((item: any, index: number) => ({
          id: uuidv4(),
          type: item.type || 'Unknown',
          locate: item.locate || null,
          param: item.param || null,
          thought: item.thought || `Action ${index + 1}`,
        }));
        actionSummary = `Extracted ${actions.length} actions from LLM response`;
      } catch (extractError) {
        throw new Error(`Failed to parse LLM response: ${extractError}`);
      }
    } else {
      // Last resort: create a single action based on the instruction
      actions = [{
        id: uuidv4(),
        type: 'Finished',
        locate: null,
        param: null,
        thought: 'LLM response could not be parsed into actions',
      }];
      actionSummary = `LLM response parsing failed, created fallback action: ${content.substring(0, 200)}...`;
    }
  }

  return { actions, actionSummary };
}

/**
 * Validate generated plan
 */
function validatePlan(plan: GeneratePlanResponse): { valid: boolean; error?: string } {
  if (!plan.planId) {
    return { valid: false, error: 'Plan ID is missing' };
  }

  if (!plan.actions || !Array.isArray(plan.actions)) {
    return { valid: false, error: 'Actions must be an array' };
  }

  if (plan.actions.length === 0) {
    return { valid: false, error: 'Plan must contain at least one action' };
  }

  if (plan.actions.length > 50) {
    return { valid: false, error: 'Plan contains too many actions (max 50)' };
  }

  // Validate each action
  for (let i = 0; i < plan.actions.length; i++) {
    const action = plan.actions[i];
    
    if (!action.id) {
      return { valid: false, error: `Action ${i + 1} is missing ID` };
    }

    if (!action.type) {
      return { valid: false, error: `Action ${i + 1} is missing type` };
    }

    const validTypes = ['Locate', 'Tap', 'Input', 'Scroll', 'KeyboardPress', 'Drag', 'Finished'];
    if (!validTypes.includes(action.type)) {
      return { valid: false, error: `Action ${i + 1} has invalid type: ${action.type}` };
    }

    // Validate locate info for relevant actions
    if (['Locate', 'Tap', 'Input'].includes(action.type)) {
      if (!action.locate || !action.locate.prompt) {
        return { valid: false, error: `Action ${i + 1} (${action.type}) missing locate information` };
      }

      if (action.locate.bbox) {
        const { x, y, width, height } = action.locate.bbox;
        if (typeof x !== 'number' || typeof y !== 'number' || 
            typeof width !== 'number' || typeof height !== 'number') {
          return { valid: false, error: `Action ${i + 1} has invalid bbox coordinates` };
        }

        if (x < 0 || y < 0 || width <= 0 || height <= 0) {
          return { valid: false, error: `Action ${i + 1} has invalid bbox dimensions` };
        }
      }
    }

    // Validate param for Input actions
    if (action.type === 'Input' && (!action.param || typeof action.param !== 'string')) {
      return { valid: false, error: `Action ${i + 1} (Input) missing or invalid param` };
    }
  }

  return { valid: true };
}