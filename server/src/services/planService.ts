import { v4 as uuidv4 } from 'uuid';
import type {
  GeneratePlanRequest,
  GeneratePlanResponse,
  PlanningAction,
} from '../types';
import { createLLMService } from './llm-service';
import fs from 'node:fs';
import path from 'node:path';

// Initialize LLM service
const llmService = createLLMService();

/**
 * Load DOM tree from file
 */
async function loadDomTree(domId: string): Promise<string> {
  try {
    const domFile = path.join(__dirname, '../../../uploads', `${domId}.json`);
    if (fs.existsSync(domFile)) {
      const data = fs.readFileSync(domFile, 'utf-8');
      const domData = JSON.parse(data);
      return JSON.stringify(domData.domTree || domData);
    }
    return '';
  } catch (error) {
    console.warn(`⚠️ Failed to load DOM tree ${domId}:`, error);
    return '';
  }
}

/**
 * 根据用户请求生成Plan
 */
export async function generatePlan(
  req: GeneratePlanRequest,
): Promise<GeneratePlanResponse> {
  const { userInstruction, conversationHistory, size, domId } = req;

  // Load DOM tree if domId is provided
  const domTreeText = domId ? await loadDomTree(domId) : '';

  // Call LLM service
  const response = await llmService.callLLM({
    messages: conversationHistory || [],
    instruction: userInstruction,
    domTreeText,
  });

  // Parse model response
  let actions: PlanningAction[] = [];
  let action_summary = response.content;
  
  try {
    const parsed = JSON.parse(response.content);
    if (Array.isArray(parsed)) {
      actions = parsed.map((item: any) => ({
        ...item,
        id: uuidv4(),
      }));
      action_summary = '大模型返回并解析成功';
    } else {
      action_summary = '大模型返回内容不是数组，原始内容已保留';
    }
  } catch (e) {
    action_summary = `大模型返回内容解析失败，原始内容：${response.content}`;
  }

  return {
    planId: uuidv4(),
    actions,
    action_summary,
  };
}
