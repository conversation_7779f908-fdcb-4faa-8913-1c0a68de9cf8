export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface Size {
  width: number;
  height: number;
}

export interface BBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface LocateInfo {
  prompt: string;
  bbox: BBox;
}

export interface PlanningAction {
  id: string;
  type: string;
  locate?: LocateInfo | null;
  param?: any;
  thought?: string;
}

export interface GeneratePlanRequest {
  userInstruction: string;
  conversationHistory: ConversationMessage[];
  size: Size;
  sessionId?: string;
  screenshotUrl?: string; // 新增，上传的截图URL
  domId?: string; // 新增，DOM树ID
}

export interface GeneratePlanResponse {
  planId: string;
  actions: PlanningAction[];
  action_summary: string;
  screenshotUrl?: string; // 新增，截图URL
}

export interface StepResult {
  step: number;
  id?: string;
  status: 'success' | 'failed';
  screenshot?: string;
  log?: string;
}

export interface UploadResultRequest {
  planId: string;
  sessionId?: string;
  results: StepResult[];
  overallStatus: 'success' | 'failed';
  errorMessage?: string;
  failedStepId?: string;
}
