/**
 * Logger utility for consistent logging across the application
 */

export interface LogData {
  [key: string]: any;
}

export interface Logger {
  debug(message: string, data?: LogData): void;
  info(message: string, data?: LogData): void;
  warn(message: string, data?: LogData): void;
  error(message: string, data?: LogData): void;
}

class ConsoleLogger implements Logger {
  private formatMessage(level: string, message: string, data?: LogData): string {
    const timestamp = new Date().toISOString();
    const baseMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    if (data && Object.keys(data).length > 0) {
      return `${baseMessage} ${JSON.stringify(data)}`;
    }
    
    return baseMessage;
  }

  debug(message: string, data?: LogData): void {
    console.debug(this.formatMessage('debug', message, data));
  }

  info(message: string, data?: LogData): void {
    console.info(this.formatMessage('info', message, data));
  }

  warn(message: string, data?: LogData): void {
    console.warn(this.formatMessage('warn', message, data));
  }

  error(message: string, data?: LogData): void {
    console.error(this.formatMessage('error', message, data));
  }
}

export const logger: Logger = new ConsoleLogger();